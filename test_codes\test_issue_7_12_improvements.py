#!/usr/bin/env python3
"""
Issue 7 ve 12 iyileştirmelerini test et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_7_improvements():
    """Issue 7 (<PERSON><PERSON><PERSON><PERSON> kate<PERSON>) iyileştirmelerini test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Başarısız olan test case'leri
    failing_cases = [
        'Elektronik Eşya',
        'Elektronik Eşya ve Aksesuarları', 
        'Ev Elektroniği'
    ]
    
    print("🔍 Issue 7 (<PERSON>r<PERSON><PERSON>) İyileştirme Testi")
    print("=" * 50)
    
    success_count = 0
    
    for i, test_value in enumerate(failing_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_7_detected = 7 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_7_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_7_detected:
            # 2. Düzeltme fonksiyonunu direkt test et
            try:
                fixed_result = processor.fix_product_category_issues(test_value)
                print(f"   🔧 Düzeltme sonucu: '{fixed_result}'")
                
                # Standart kategori mi?
                standard_categories = [
                    'Elektronik', 'Ev Aletleri', 'Giyim', 'Mobilya', 'Kitap',
                    'Oyuncak', 'Spor', 'Kozmetik', 'Gıda', 'Temizlik', 'Bahçe', 'Otomotiv'
                ]
                
                if fixed_result in standard_categories:
                    print(f"   ✅ Standart kategori: '{fixed_result}'")
                else:
                    print(f"   ⚠️ Standart olmayan kategori: '{fixed_result}'")
                    
            except Exception as e:
                print(f"   ❌ Düzeltme hatası: {e}")
        
        # 3. Process_value ile tam test
        try:
            processed_value, fixes = processor.process_value(test_value, "test_column")
            
            # Issue 7 düzeltmesi uygulandı mı?
            issue_7_fixes = [f for f in fixes if f['issue_id'] == 7]
            if issue_7_fixes:
                success_count += 1
                print(f"   ✅ Agresif strateji başarılı: '{processed_value}'")
                for fix in issue_7_fixes:
                    print(f"      - {fix['original']} → {fix['fixed']}")
            else:
                print(f"   ❌ Agresif strateji başarısız - düzeltme uygulanmadı")
                
        except Exception as e:
            print(f"   ❌ Process_value hatası: {e}")
    
    print(f"\n📊 Issue 7 İyileştirme Sonuçları:")
    print(f"   📝 Test sayısı: {len(failing_cases)}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {(success_count/len(failing_cases))*100:.1f}%")
    
    return success_count == len(failing_cases)

def test_issue_12_improvements():
    """Issue 12 (Müşteri kategori) iyileştirmelerini test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Başarısız olan test case'i
    failing_cases = ['Perakende Satış']
    
    print("\n🔍 Issue 12 (Müşteri Kategori) İyileştirme Testi")
    print("=" * 50)
    
    success_count = 0
    
    for i, test_value in enumerate(failing_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_12_detected = 12 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_12_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_12_detected:
            # 2. Düzeltme fonksiyonunu direkt test et
            try:
                fixed_result = processor.fix_customer_category_issues(test_value)
                print(f"   🔧 Düzeltme sonucu: '{fixed_result}'")
                
                # Standart kategori mi?
                standard_categories = ['Toptan', 'Bayi', 'Distribütör', 'Kurumsal', 'Perakende', 'Bireysel']
                
                if fixed_result in standard_categories:
                    print(f"   ✅ Standart kategori: '{fixed_result}'")
                else:
                    print(f"   ⚠️ Standart olmayan kategori: '{fixed_result}'")
                    
            except Exception as e:
                print(f"   ❌ Düzeltme hatası: {e}")
        
        # 3. Process_value ile tam test
        try:
            processed_value, fixes = processor.process_value(test_value, "test_column")
            
            # Issue 12 düzeltmesi uygulandı mı?
            issue_12_fixes = [f for f in fixes if f['issue_id'] == 12]
            if issue_12_fixes:
                success_count += 1
                print(f"   ✅ Agresif strateji başarılı: '{processed_value}'")
                for fix in issue_12_fixes:
                    print(f"      - {fix['original']} → {fix['fixed']}")
            else:
                print(f"   ❌ Agresif strateji başarısız - düzeltme uygulanmadı")
                
        except Exception as e:
            print(f"   ❌ Process_value hatası: {e}")
    
    print(f"\n📊 Issue 12 İyileştirme Sonuçları:")
    print(f"   📝 Test sayısı: {len(failing_cases)}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {(success_count/len(failing_cases))*100:.1f}%")
    
    return success_count == len(failing_cases)

if __name__ == "__main__":
    print("🧪 Issue 7 ve 12 İyileştirme Test Süreci")
    print("=" * 60)
    
    # Issue 7 iyileştirmelerini test et
    issue_7_success = test_issue_7_improvements()
    
    # Issue 12 iyileştirmelerini test et
    issue_12_success = test_issue_12_improvements()
    
    print(f"\n" + "=" * 60)
    print(f"🏆 İyileştirme Sonuçları:")
    print(f"   Issue 7 iyileştirme: {'✅ Başarılı' if issue_7_success else '❌ Başarısız'}")
    print(f"   Issue 12 iyileştirme: {'✅ Başarılı' if issue_12_success else '❌ Başarısız'}")
    
    if issue_7_success and issue_12_success:
        print(f"   🎉 Tüm iyileştirmeler başarılı!")
    else:
        print(f"   ⚠️ Bazı iyileştirmeler başarısız")
