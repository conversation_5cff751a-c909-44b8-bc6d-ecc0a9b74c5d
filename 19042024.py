import pandas as pd
import datetime
import numpy as np
from datetime import timedelta
import warnings
warnings.filterwarnings("ignore")


pd.set_option("display.width", 500)
pd.set_option("display.float_format", lambda x: '%.2f' % x)
pd.set_option('display.max_columns',100)


df = pd.read_excel("18-24 Nisan Gift.xlsx", sheet_name="2018-2023") #144611

df.isnull().sum()

df[df["VergiNo"].isnull()]
df[df["VergiNo"]==0]

# vergino alanında 2 tane boşluk var, biri multinet, onu dolduruyorum:
df[df["Firma"] == "Multinet Kurumsal Hiz.A.Ş."]
df.loc[df["Firma"] == "Multinet Kurumsal Hiz.A.Ş.","VergiNo"]=6230090752

df[df["VergiNo"].isnull()]
df[df["Firma"] == "ESNEK YAN HAKLAR DANIŞMANLIK HİZMETLERİ LTD. ŞTİ."]
df.loc[df["Firma"] == "ESNEK YAN HAKLAR DANIŞMANLIK HİZMETLERİ LTD. ŞTİ.","VergiNo"]=3800523520

df[df["VergiNo"].isnull()] #kalmadı


#daha önceki datada DSİ ile ilgili bir çoklama vardı onu bi kontrol edeyim:
df[df["Firma"].str.startswith("DSİ")] #1754 kayıt var
df[df["Firma"].str.startswith("DSİ")]["Firma"].value_counts()
# DSİ                                 992 --> 1
# DSİ 12 BLG.                         325 --> 2
# DSİ 12. BÖLGE MÜDÜRLÜĞÜ KAYSERİ      35 --> 2
# DSİ. 12 BLG                           5 --> 2
# DSİ 18 BLG.                         171 --> 3
# DSİ 13 BLG.                         158 --> 4
# DSİ 13 BÖLGE MÜDÜRLÜĞÜ               20 --> 4
# DSİ 13 BÖLGE MÜDÜRLÜĞÜ - ANTALYA      4 --> 4
# DSİ. 13 BLG                           1 --> 4
# DSİ. 19 BLG                          35 --> 5
# DSİ 19. BÖLGE MÜDÜRLÜĞÜ SİVAS         5 --> 5
# DSİ 25. BÖLGE MÜDÜRLÜĞÜ               1 --> 6
# DSİ 14. BÖLGE MÜDÜRLÜĞÜ               1 --> 7
# DSİ. 4 BLG                            1 --> 8


#DSİ ile ilgili isim tekilleştirmesi:
df.loc[df["Firma"]=="DSİ 12 BLG.", "Firma"] = "DSİ 12. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 12. BLG", "Firma"] = "DSİ 12. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 12. BÖLGE MÜDÜRLÜĞÜ KAYSERİ", "Firma"] = "DSİ 12. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 13 BLG.", "Firma"] = "DSİ 13. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 13. BLG", "Firma"] = "DSİ 13. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ. 13 BLG", "Firma"] = "DSİ 13. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 13 BÖLGE MÜDÜRLÜĞÜ", "Firma"] = "DSİ 13. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 13 BÖLGE MÜDÜRLÜĞÜ - ANTALYA", "Firma"] = "DSİ 13. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 13. BÖLGE MÜDÜRLÜĞÜ ANTALYA", "Firma"] = "DSİ 13. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"].str.startswith("DSİ 13"), "Firma"] = "DSİ 13. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 18 BLG.", "Firma"] = "DSİ 18. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 18. BLG", "Firma"] = "DSİ 18. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 19. BLG", "Firma"] = "DSİ 19. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ 19. BÖLGE MÜDÜRLÜĞÜ SİVAS", "Firma"] = "DSİ 19. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ. 12 BLG", "Firma"] = "DSİ 12. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ. 19 BLG", "Firma"] = "DSİ 19. BÖLGE MÜDÜRLÜĞÜ"
df.loc[df["Firma"]=="DSİ. 4 BLG", "Firma"] = "DSİ 4. BÖLGE MÜDÜRLÜĞÜ"

df[df["Firma"].str.startswith("DSİ")]["Firma"].value_counts()
df[df["Firma"].str.startswith("DSI")]["Firma"].value_counts()
df[df["Firma"].str.startswith("DEVLET SU")]["Firma"].value_counts()
df[df["Firma"].str.startswith("dsi")]["Firma"].value_counts()
df[df["Firma"].str.startswith("DSi")]["Firma"].value_counts()
#DSİ tekilleştirmesi tamam


# bazı alanlar ekleyeceğim:
df["Miktar"] = 1
df['FaturaTarihi'] = pd.to_datetime(dict(year=df["Yıl"], month=df["Aynum"], day=1))
df["VergiNoStr"] = df["VergiNo"].astype('Int64').astype('str')
df.info()

df[["VergiNo", "VergiNoStr"]]

#şimdi diğer mükerrer kayıtlara bakalım:
df_tekil_firms = pd.DataFrame(df.groupby(["Firma"]).agg({"Miktar": "sum"}).sort_values("Firma"))  #11788
df_tekil_firms = df_tekil_firms.reset_index()

#df_tekil_firms = pd.DataFrame(df.groupby(["VergiNoStr"]).agg({"Miktar": "sum"}).sort_values("VergiNoStr"))  #12628
#df_tekil_firms = df_tekil_firms.reset_index()
df_tekil_firms.to_excel("RFM3/widgets/tekilfirms.xlsx")



#df_tekil_firms_from_vkn = pd.DataFrame(df.groupby(["VergiNoStr"]).agg({"Miktar": "sum"}).sort_values("VergiNoStr")) #12628 tane tekil vkn var, bu yüzden isimle devam edeceğim:


def find_similar_rows(mydf, start_from=10):
    shape = mydf.shape[0]
    df_similars =[]
    for index, row in mydf.iterrows():
        if index < shape-1:
            if (mydf.loc[index,"Firma"][0:start_from] == mydf.loc[index+1,"Firma"][0:start_from]): #& (df.loc[index,"VergiNoStr"] != df.loc[index+1,"VergiNoStr"]):
                #print(row)
                df_similars.append(mydf.loc[index,["Firma"]])
                df_similars.append(mydf.loc[index+1, ["Firma"]])
    return df_similars


sim = pd.DataFrame(find_similar_rows(df_tekil_firms,10)) #1326
sim.columns = ["Firma"]
similars = sim.merge(df.drop_duplicates(subset=["Firma"]), how="left")
similars = similars[["Firma", "VergiNoStr"]]
#similars["Miktar"]=1
#similars = similars.groupby(["Firma", "VergiNoStr"]).agg({"Miktar":"sum"}).reset_index() #841

similars.to_excel("RFM3/widgets/similarfirms.xlsx")
df.info()

def manage_duplicates(main_df, similar_firms):
    # İsimleri farklı olup vergi numaraları aynı olanları tek isimde birleştir
    # İsimleri farklı olup vergi numaraları farklı olanlar farklı şirketlerdir, birşey yapma
    for index, row in similar_firms.iterrows():
        #print(row)
        main_df.loc[main_df["VergiNoStr"]==row["VergiNoStr"], ["Firma","VergiNoStr"]] = [row["Firma"],row["VergiNoStr"]]
    return main_df

df = manage_duplicates(df, similars)
df.info()

#şimdi vknleri eşleşenlerden sonraki duruma bir daha bakalım:
df_tekil_firms = pd.DataFrame(df.groupby(["Firma"]).agg({"Miktar": "sum"}).sort_values("Firma"))  #11310
df_tekil_firms = df_tekil_firms.reset_index()

sim = pd.DataFrame(find_similar_rows(df_tekil_firms,10)) #486
sim.columns = ["Firma"]
similars = sim.merge(df.drop_duplicates(subset=["Firma"]), how="left")
similars = similars[["Firma", "VergiNoStr"]]
similars.to_excel("RFM3/widgets/similarfirms.xlsx")

df[df["VergiNoStr"]=="1210046435"]["Firma"].value_counts()
df[df["Firma"].str.startswith("ABALIOĞLU")]["Firma"].unique()

tekil_isimler = pd.DataFrame(df["Firma"].unique())

df.info()
df.columns

df[df["Ciro"]<=0] #76721
df[df["Ciro"]<0] #378
df[df["Ciro"]==0] #76343


df[["VergiNo", "VergiNoStr"]]


## similarfirms firmaların üzerinde çalıştım, tek tek elle isimleri düzelttim şimdi o listeyi kullanacağım:
fs = pd.read_excel("RFM3/widgets/similarfirms_revised.xlsx")


for i, r in fs.iterrows():
    df.loc[df["Firma"] ==r["Firma"],["Firma","VergiNoStr"]]= [r["Yeni_ISIM"],r["VergiNoStr"]]
    #df.loc[df["VergiNoStr"]==r["VergiNoStr"],"Firma"]= r["Firma"]
print("bitti")

df_tekil_firms = pd.DataFrame(df.groupby("Firma").agg({"Miktar": "sum"}).sort_values("Firma")) #11264
df_tekil_firms = df_tekil_firms.reset_index()
sim = pd.DataFrame(find_similar_rows(df_tekil_firms)) #394
sim.columns = ["Firma"]
similars = sim.merge(df.drop_duplicates(subset=["Firma"]), how="left") #394
similars = similars[["Firma", "VergiNoStr"]]

#duplike kayıtlara bi bakalım
df = manage_duplicates(df, similars)


df_tekil_firms = pd.DataFrame(df.groupby("Firma").agg({"Miktar": "sum"}).sort_values("Firma")) #11263
df_tekil_firms = df_tekil_firms.reset_index()
sim = pd.DataFrame(find_similar_rows(df_tekil_firms)) #392
sim.columns = ["Firma"]
similars = sim.merge(df.drop_duplicates(subset=["Firma"]), how="left") #392
similars = similars[["Firma", "VergiNoStr"]]


# Recency : Yenilik, Sıcaklık. (Analizin yapıldığı tarih - Müşterinin yaptığı son alışverişin tarihi)
# Frequency : Müşterinin yaptığı toplam satın alma
# Monetary : Müşterinin yaptığı toplam satın almalar neticesinde bıraktığı toplam parasal değer
today_date = df["FaturaTarihi"].max()+ timedelta(days=2) #np.datetime64(datetime.date.today())

rfm = df.groupby("Firma").agg({
    "FaturaTarihi": lambda tarih: (today_date - tarih.max()).days,
    "Miktar": lambda Miktar: Miktar.sum(),
    "Ciro": lambda Ciro: Ciro.sum()
})
rfm = rfm.reset_index()

rfm.columns = ["Firma","recency", "frequency", "monetary"]
rfm = rfm[rfm["monetary"] > 0]

# RFM SKORLARININ HAZIRLANMASI
rfm["recency_score"] = pd.qcut(rfm["recency"].rank(method="first"), 5, labels=[5, 4, 3, 2, 1])
rfm["frequency_score"] = pd.qcut(rfm["frequency"].rank(method="first"), 5, labels=[1, 2, 3, 4, 5])
rfm["monetary_score"] = pd.qcut(rfm["monetary"].rank(method="first"), 5, labels=[1, 2, 3, 4, 5])


# rfm skorları kategorik değere dönüştürülüp rfm'e eklendi
rfm["RFM_SCORE"] = rfm["recency_score"].astype(str) + rfm["frequency_score"].astype(str)

# SEGMENTLERİN OLUŞTURULMASI
seg_map = {
    r'[1-2][1-2]': 'hibernating',
    r'[1-2][3-4]': 'at_Risk',
    r'[1-2]5': 'cant_loose',
    r'3[1-2]': 'about_to_sleep',
    r'33': 'need_attention',
    r'[3-4][4-5]': 'loyal_customers',
    r'41': 'promising',
    r'51': 'new_customers',
    r'[4-5][2-3]': 'potential_loyalists',
    r'5[4-5]': 'champions'
}
rfm["segment"] = rfm["RFM_SCORE"].replace(seg_map, regex=True)
rfm = rfm[["Firma", "recency", "frequency", "monetary", "segment"]]
result_rfm1 = rfm
df_new = df[["Firma","VergiNoStr","İl", "AnaSektör", "AltSektör"]]
result_rfm = pd.merge(rfm, df_new, how="left", on= "Firma")
result_rfm = rfm.merge(df_new.drop_duplicates(subset=['Firma']), how="left", on= "Firma")
result_rfm = result_rfm.groupby(['Firma']).first().reset_index()
today = datetime.date.today().strftime("%d-%m-%Y")
dosyaadi = "RFM3/result/multinet-"+today+"-rfm.xlsx"
result_rfm.to_excel(dosyaadi)

#### pivot with ürün
df_rfm = pd.merge(result_rfm, df, how="inner", on="Firma")
df_rfm.columns
df_rfm = df_rfm[['Firma', 'recency', 'frequency', 'monetary', 'segment', 'VergiNoStr_x', 'İl_x', 'AnaSektör_x', 'AltSektör_x', 'Ürün']]
df_rfm["Miktar"] = 1

rfm_pivot = df_rfm.groupby(["Firma", "Ürün" ])["Miktar"].aggregate("sum").unstack()
rfm_pivot = rfm_pivot.fillna(0)
rfm_pivot = rfm_pivot.reset_index()
rfm_pivot.columns

rfm_nihai_pivot = pd.merge(result_rfm, rfm_pivot.drop_duplicates(subset=['Firma']), on='Firma', how='left')

rfm_nihai_pivot.columns

rfm_nihai_pivot.to_excel("RFM3/result/multinet-"+today+"-rfm-with-urun.xlsx")


eski= pd.read_excel("MLTNT2/result/multinet-01-12-2023-rfmyeni.xlsx")

karsilastirma = pd.merge(result_rfm, eski, on="Firma", how="left")

eski["var"]=0
for i,r in result_rfm.iterrows():
    eski.loc[eski["Firma"]==r["Firma"],"var"] = 1
print("bitti")

eski[eski["var"]==0]

karsilastirma.columns
karsilastirma = karsilastirma[['Firma', 'recency_x', 'frequency_x', 'monetary_x', 'segment_x', 'VergiNoStr_x', 'İl_x',
                               'AnaSektör_x', 'AltSektör_x','recency_y', 'frequency_y', 'monetary_y', 'segment_y']]

karsilastirma.columns = [['Firma', 'recencyYeni', 'frequencyYeni', 'monetaryYeni', 'segmentYeni', 'VergiNoStr',
                  'İl', 'AnaSektör', 'AltSektör','recencyEski', 'frequencyEski', 'monetaryEski', 'segmentEski']]

karsilastirma = karsilastirma[['VergiNoStr','Firma', 'recencyYeni', 'frequencyYeni', 'monetaryYeni', 'segmentYeni',
                               'recencyEski', 'frequencyEski', 'monetaryEski', 'segmentEski',
                               'İl', 'AnaSektör', 'AltSektör']]

karsilastirma.to_excel("RFM3/result/multinet-"+today+"-rfm-karsilastirma.xlsx")


df[df["Firma"].str.startswith("CCPB")]
import matplotlib.pyplot as plt

yillaragorecirolar= df.groupby("Yıl").agg({"Ciro":"sum"}).reset_index()
plt.bar(yillaragorecirolar["Yıl"], yillaragorecirolar["Ciro"])
plt.title('Yıllara Göre Ciro')
plt.xlabel('Yıl')
plt.ylabel('Cirolar')
plt.show()

aylaragorecirolar= df.groupby("Ay").agg({"Ciro":"sum"}).reset_index()
plt.bar(aylaragorecirolar["Ay"], aylaragorecirolar["Ciro"])
plt.title('Örnek Çubuk Grafik')
plt.xlabel('Ay')
plt.ylabel('Cirolar')
plt.show()


son2yilaylaragorecirolar= df[df["Yıl"]>2022].groupby("Dönem").agg({"Ciro":"sum"}).reset_index()
plt.bar(son2yilaylaragorecirolar["Dönem"], son2yilaylaragorecirolar["Ciro"])
plt.title('Son 2 yılda aylara göre grafik')
plt.xlabel('Dönem')
plt.ylabel('Cirolar')
plt.show()

donemler = ['Ocak 2023', 'Şubat 2023', 'Mart 2023', 'Nisan 2023', 'Mayıs 2023', 'Haziran 2023', 'Temmuz 2023', 'Ağustos 2023', 'Eylül 2023', 'Ekim 2023', 'Kasım 2023', 'Aralık 2023', 'Ocak 2024', 'Şubat 2024', 'Mart 2024', 'Nisan 2024']

plt.figure(figsize=(10, 6)) # Grafik boyutunu belirle
plt.bar(donemler, son2yilaylaragorecirolar['Ciro'], color='skyblue') # Çubuk grafiği oluştur
plt.xlabel('Dönem') # x ekseni etiketi
plt.ylabel('Ciro') # y ekseni etiketi
plt.title('Son 2 Yıl Ciro Dönem Grafiği') # Grafik başlığı
plt.xticks(rotation=45) # x ekseni etiketlerini 45 derece döndür
plt.grid(axis='y', linestyle='--', alpha=0.7) # y ekseninde ızgara ekle
plt.tight_layout() # Grafik düzenleme
plt.show() # Grafik göster

df[df["Yıl"]>2022]["Dönem"].unique()


son4yilaylaragorecirolar= df.groupby("Dönem").agg({"Ciro":"sum"}).reset_index()
plt.bar(son4yilaylaragorecirolar["Dönem"], son4yilaylaragorecirolar["Ciro"])
plt.title('Son 4 yılda aylara göre grafik')
plt.xlabel('Dönem')
plt.ylabel('Cirolar')
plt.show()

son4yilaylaragorecirolar= df[df["Yıl"]>2019].groupby("Dönem").agg({"Ciro":"sum"}).reset_index()

donemler = ['Ocak 2020', 'Şubat 2020', 'Mart 2020', 'Nisan 2020', 'Mayıs 2020', 'Haziran 2020', 'Temmuz 2020', 'Ağustos 2020', 'Eylül 2020', 'Ekim 2020', 'Kasım 2020', 'Aralık 2020',
            'Ocak 2021', 'Şubat 2021', 'Mart 2021', 'Nisan 2021', 'Mayıs 2021', 'Haziran 2021', 'Temmuz 2021', 'Ağustos 2021', 'Eylül 2021', 'Ekim 2021', 'Kasım 2021', 'Aralık 2021',
            'Ocak 2022', 'Şubat 2022', 'Mart 2022', 'Nisan 2022', 'Mayıs 2022', 'Haziran 2022', 'Temmuz 2022', 'Ağustos 2022', 'Eylül 2022', 'Ekim 2022', 'Kasım 2022', 'Aralık 2022',
            'Ocak 2023', 'Şubat 2023', 'Mart 2023', 'Nisan 2023', 'Mayıs 2023', 'Haziran 2023', 'Temmuz 2023', 'Ağustos 2023', 'Eylül 2023', 'Ekim 2023', 'Kasım 2023', 'Aralık 2023',
            'Ocak 2024', 'Şubat 2024', 'Mart 2024', 'Nisan 2024']

plt.figure(figsize=(15, 8)) # Grafik boyutunu belirle
plt.bar(donemler, son4yilaylaragorecirolar['Ciro'], color='skyblue') # Çubuk grafiği oluştur
plt.xlabel('Dönem') # x ekseni etiketi
plt.ylabel('Ciro') # y ekseni etiketi
plt.title('Son 4 Yıl Ciro Dönem Grafiği') # Grafik başlığı
plt.xticks(rotation=90) # x ekseni etiketlerini 45 derece döndür
plt.grid(axis='y', linestyle='--', alpha=0.7) # y ekseninde ızgara ekle
plt.tight_layout() # Grafik düzenleme
plt.show() # Grafik göster

df["Ürün"].unique()

urunleregorecirolar= df[df["Yıl"]>2019].groupby("Ürün").agg({"Ciro":"sum"}).reset_index()

urunleregorecirolar.sort_values(by='Ciro', ascending=False)
#                                Ürün          Ciro
# 3                              Ayni 1316474211.16
# 1           A101 Hediye Kart Bedeli  766597268.62
# 34                          Gıda %1  570248570.68
# 53                       MediaMarkt  476669013.43
# 64                     Teknosa Kart  275458799.02


aynicirolar= df[(df["Yıl"]>2019) & (df["Ürün"]=="Ayni")].groupby(["Dönem","Ürün"]).agg({"Ciro":"sum"}).reset_index()
donemler = ['Ocak 2020', 'Şubat 2020', 'Mart 2020', 'Nisan 2020', 'Mayıs 2020', 'Haziran 2020', 'Temmuz 2020', 'Ağustos 2020', 'Eylül 2020', 'Ekim 2020', 'Kasım 2020', 'Aralık 2020',
            'Ocak 2021', 'Şubat 2021', 'Mart 2021', 'Nisan 2021', 'Mayıs 2021', 'Haziran 2021', 'Temmuz 2021', 'Ağustos 2021', 'Eylül 2021', 'Ekim 2021', 'Kasım 2021', 'Aralık 2021',
            'Ocak 2022', 'Şubat 2022', 'Mart 2022', 'Nisan 2022', 'Mayıs 2022', 'Haziran 2022', 'Temmuz 2022', 'Ağustos 2022', 'Eylül 2022', 'Ekim 2022', 'Kasım 2022', 'Aralık 2022',
            'Ocak 2023', 'Şubat 2023', 'Mart 2023', 'Nisan 2023', 'Mayıs 2023', 'Haziran 2023', 'Temmuz 2023', 'Ağustos 2023', 'Eylül 2023', 'Ekim 2023', 'Kasım 2023', 'Aralık 2023',
            'Ocak 2024', 'Şubat 2024', 'Mart 2024', 'Nisan 2024']
plt.figure(figsize=(15, 8)) # Grafik boyutunu belirle
plt.bar(donemler, aynicirolar['Ciro'], color='skyblue') # Çubuk grafiği oluştur
plt.xlabel('Dönem') # x ekseni etiketi
plt.ylabel('Ciro') # y ekseni etiketi
plt.title('Ayni Ciro Dönem Grafiği') # Grafik başlığı
plt.xticks(rotation=90) # x ekseni etiketlerini 45 derece döndür
plt.grid(axis='y', linestyle='--', alpha=0.7) # y ekseninde ızgara ekle
plt.tight_layout() # Grafik düzenleme
plt.show() # Grafik göster


a101cirolar= df[(df["Yıl"]>2019) & (df["Ürün"]=="A101 Hediye Kart Bedeli")].groupby(["Dönem","Ürün"]).agg({"Ciro":"sum"}).reset_index()
donemler = ['Mart 2020', 'Nisan 2020', 'Mayıs 2020', 'Haziran 2020', 'Temmuz 2020', 'Ağustos 2020', 'Eylül 2020', 'Ekim 2020', 'Kasım 2020', 'Aralık 2020',
            'Ocak 2021', 'Şubat 2021', 'Mart 2021', 'Nisan 2021', 'Mayıs 2021', 'Haziran 2021', 'Temmuz 2021', 'Ağustos 2021', 'Eylül 2021', 'Ekim 2021', 'Kasım 2021', 'Aralık 2021',
            'Ocak 2022', 'Şubat 2022', 'Mart 2022', 'Nisan 2022', 'Mayıs 2022', 'Haziran 2022', 'Temmuz 2022', 'Ağustos 2022', 'Eylül 2022', 'Ekim 2022', 'Kasım 2022', 'Aralık 2022',
            'Ocak 2023', 'Şubat 2023', 'Mart 2023', 'Nisan 2023', 'Mayıs 2023', 'Haziran 2023', 'Temmuz 2023', 'Ağustos 2023', 'Eylül 2023', 'Ekim 2023', 'Kasım 2023', 'Aralık 2023',
            'Ocak 2024', 'Şubat 2024', 'Mart 2024', 'Nisan 2024']
plt.figure(figsize=(15, 8)) # Grafik boyutunu belirle
plt.bar(donemler, a101cirolar['Ciro'], color='skyblue') # Çubuk grafiği oluştur
plt.xlabel('Dönem') # x ekseni etiketi
plt.ylabel('Ciro') # y ekseni etiketi
plt.title('A101 Hediye Kart Bedeli Ciro Dönem Grafiği') # Grafik başlığı
plt.xticks(rotation=90) # x ekseni etiketlerini 45 derece döndür
plt.grid(axis='y', linestyle='--', alpha=0.7) # y ekseninde ızgara ekle
plt.tight_layout() # Grafik düzenleme
plt.show() # Grafik göster


gidacirolar= df[(df["Yıl"]>2019) & (df["Ürün"]=="Gıda %1")].groupby(["Dönem","Ürün"]).agg({"Ciro":"sum"}).reset_index()
donemler = [ 'Şubat 2022', 'Mart 2022', 'Nisan 2022', 'Mayıs 2022', 'Haziran 2022', 'Temmuz 2022', 'Ağustos 2022', 'Eylül 2022', 'Ekim 2022', 'Kasım 2022', 'Aralık 2022',
            'Ocak 2023', 'Şubat 2023', 'Mart 2023', 'Nisan 2023', 'Mayıs 2023', 'Haziran 2023', 'Temmuz 2023', 'Ağustos 2023', 'Eylül 2023', 'Ekim 2023', 'Kasım 2023', 'Aralık 2023',
            'Ocak 2024', 'Şubat 2024', 'Mart 2024', 'Nisan 2024']
plt.figure(figsize=(15, 8)) # Grafik boyutunu belirle
plt.bar(donemler, gidacirolar['Ciro'], color='skyblue') # Çubuk grafiği oluştur
plt.xlabel('Dönem') # x ekseni etiketi
plt.ylabel('Ciro') # y ekseni etiketi
plt.title('Gıda %1 Ciro Dönem Grafiği') # Grafik başlığı
plt.xticks(rotation=90) # x ekseni etiketlerini 45 derece döndür
plt.grid(axis='y', linestyle='--', alpha=0.7) # y ekseninde ızgara ekle
plt.tight_layout() # Grafik düzenleme
plt.show() # Grafik göster


uruncirolar= df[(df["Yıl"]>2019) & ((df["Ürün"]=="Ayni") |
                                    (df["Ürün"]=="A101 Hediye Kart Bedeli") |
                                    (df["Ürün"]=="Gıda %1") |
                                    (df["Ürün"]=="MediaMarkt"))].groupby(["Dönem","Ürün"]).agg({"Ciro":"sum"}).reset_index()

donemler = ['Ocak 2020', 'Şubat 2020', 'Mart 2020', 'Nisan 2020', 'Mayıs 2020', 'Haziran 2020', 'Temmuz 2020', 'Ağustos 2020', 'Eylül 2020', 'Ekim 2020', 'Kasım 2020', 'Aralık 2020',
            'Ocak 2021', 'Şubat 2021', 'Mart 2021', 'Nisan 2021', 'Mayıs 2021', 'Haziran 2021', 'Temmuz 2021', 'Ağustos 2021', 'Eylül 2021', 'Ekim 2021', 'Kasım 2021', 'Aralık 2021',
            'Ocak 2022', 'Şubat 2022', 'Mart 2022', 'Nisan 2022', 'Mayıs 2022', 'Haziran 2022', 'Temmuz 2022', 'Ağustos 2022', 'Eylül 2022', 'Ekim 2022', 'Kasım 2022', 'Aralık 2022',
            'Ocak 2023', 'Şubat 2023', 'Mart 2023', 'Nisan 2023', 'Mayıs 2023', 'Haziran 2023', 'Temmuz 2023', 'Ağustos 2023', 'Eylül 2023', 'Ekim 2023', 'Kasım 2023', 'Aralık 2023',
            'Ocak 2024', 'Şubat 2024', 'Mart 2024', 'Nisan 2024']

# Grafik alanını 4'e bölmek için subplotlar oluştur
fig, axs = plt.subplots(2, 2, figsize=(12, 8))

# Her bir ürün için ayrı alt grafik oluştur
for i, urun in enumerate(uruncirolar['Ürün'].unique()):
    # Ürüne özel DataFrame oluşturma
    df_urun = uruncirolar[uruncirolar['Ürün'] == urun]

    # Alt grafik oluşturma
    ax = axs[i // 2, i % 2]
    ax.bar(range(len(df_urun)), df_urun['Ciro'], color='skyblue')
    ax.set_title(f'{urun} Ciro Dönem Grafiği')
    ax.set_xlabel('Dönem')
    ax.set_ylabel('Ciro')
    ax.set_xticks(range(len(df_urun)))  # x eksenindeki konumları belirt
    ax.set_xticklabels(donemler)  # x eksenindeki etiketleri belirt
    ax.tick_params(axis='x', rotation=45)

# Eksik alt grafiklerin gizlenmesi
for i in range(len(uruncirolar['Ürün'].unique()), len(axs.flat)):
    axs.flatten()[i].axis('off')

# Alt grafikler arasındaki düzen
plt.tight_layout()

# Grafik gösterme
plt.show()

uruncirolar.head(20)