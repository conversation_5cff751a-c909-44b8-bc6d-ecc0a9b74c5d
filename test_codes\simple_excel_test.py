#!/usr/bin/env python3
"""
Basit Excel İşleme Testi
"""

import pandas as pd
import sys
import os

# Ana dizini path'e ekle
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from component_cards import ExcelDataProcessor, DataQualityProcessor, LLMClientFactory
import time

def simple_excel_test():
    """Basit Excel işleme testi"""
    print("🧪 BASİT EXCEL İŞLEME TESTİ")
    print("=" * 50)
    
    # 1. Test Excel dosyası oluştur
    test_data = {
        'Gelir': ['1000 TL', '500 USD'],
        'Tarih': ['25-12-2023', '2023/12/25'],
        'Eksik_Veri': ['N/A', '']
    }
    
    df = pd.DataFrame(test_data)
    excel_path = 'test_codes/simple_test.xlsx'
    df.to_excel(excel_path, index=False, engine='openpyxl')
    
    print(f"✅ Test dosyası oluşturuldu: {excel_path}")
    print(f"📊 Boyut: {df.shape[0]} satır, {df.shape[1]} kolon")
    
    # 2. Provider kontrolü
    from config import MAX_WORKERS
    print(f"⚙️ MAX_WORKERS: {MAX_WORKERS}")
    
    try:
        # Provider havuzu kontrolü
        from component_cards import ProviderPool
        active_providers = ProviderPool.get_active_providers()
        print(f"🔗 Aktif provider'lar: {len(active_providers)}")
        for provider in active_providers:
            print(f"   - {provider['name']} ({provider['type']})")
        
        if not active_providers:
            print("❌ Hiç aktif provider yok!")
            return False
            
    except Exception as e:
        print(f"❌ Provider kontrolü hatası: {e}")
        return False
    
    # 3. ExcelDataProcessor oluştur
    try:
        processor = ExcelDataProcessor(max_workers=2)  # Test için 2 worker
        print("✅ ExcelDataProcessor oluşturuldu")
    except Exception as e:
        print(f"❌ ExcelDataProcessor hatası: {e}")
        return False
    
    # 4. Dosyayı işle
    output_path = 'test_codes/simple_test_fixed.xlsx'
    log_path = 'test_codes/simple_test_log.xlsx'
    
    try:
        print("🚀 Excel dosyası işleniyor...")
        start_time = time.time()
        
        summary = processor.process_excel_file(excel_path, output_path, log_path, use_streamlit=False)
        
        processing_time = time.time() - start_time
        print(f"✅ İşleme tamamlandı! ({processing_time:.2f} saniye)")
        
        # 5. Sonuçları göster
        print(f"\n📊 SONUÇLAR:")
        print(f"   📝 Toplam hücre: {summary['total_cells']}")
        print(f"   🚨 Bulunan issue: {summary['issues_found']}")
        print(f"   🔧 Düzeltilen issue: {summary['issues_fixed']}")
        print(f"   ⏱️ İşleme süresi: {summary['processing_time_seconds']:.2f} saniye")
        print(f"   ⚡ Hücre/saniye: {summary['cells_per_second']:.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ İşleme hatası: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_excel_test()
    
    if success:
        print(f"\n🎉 TEST BAŞARILI!")
        print(f"✅ Excel dosyası işleme sistemi çalışıyor")
        print(f"✅ 65 issue tespit ve düzeltme aktif")
        print(f"✅ MAX_WORKERS paralel işleme çalışıyor")
    else:
        print(f"\n❌ TEST BAŞARISIZ!")
