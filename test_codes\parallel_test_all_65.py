#!/usr/bin/env python3
"""
Paralel Test - Tüm 65 Issue
von.md'deki tüm problemleri paralel test et
"""

import multiprocessing as mp
import time
from datetime import datetime
from collections import defaultdict
import json

def worker_test_issue(args):
    """Worker fonksiyonu - Her process kendi LLM client'ı"""
    issue_id, test_cases, worker_id = args
    
    # Her worker kendi import'unu yapar (process isolation)
    from component_cards import DataQualityProcessor, OllamaLLMClient
    
    print(f"🚀 Worker {worker_id} başladı - Issue {issue_id}")
    
    results = []
    start_time = time.time()
    
    try:
        # Her worker kendi LLM client'ı oluşturur - LOAD BALANCING
        llm_client = OllamaLLMClient(worker_id=worker_id)
        processor = DataQualityProcessor(llm_client)
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                # Tespit
                detected_issues = processor.detect_issues(test_case, "test_column")
                detected = issue_id in detected_issues
                
                # Düzeltme (sadece tespit edilirse)
                fixed = False
                if detected:
                    processed_value, fixes = processor.process_value(test_case, "test_column")
                    issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                    fixed = len(issue_fixes) > 0
                
                results.append({
                    'test_case': test_case,
                    'detected': detected,
                    'fixed': fixed,
                    'worker_id': worker_id
                })
                
                # Progress
                if i % 2 == 0:
                    print(f"   Worker {worker_id}: {i}/{len(test_cases)} tamamlandı")
                    
            except Exception as e:
                print(f"   ❌ Worker {worker_id} test hatası ({test_case}): {e}")
                results.append({
                    'test_case': test_case,
                    'detected': False,
                    'fixed': False,
                    'worker_id': worker_id,
                    'error': str(e)
                })
        
        execution_time = time.time() - start_time
        print(f"✅ Worker {worker_id} tamamlandı - Issue {issue_id} - {execution_time:.1f}s")
        
        return {
            'issue_id': issue_id,
            'results': results,
            'execution_time': execution_time,
            'worker_id': worker_id
        }
        
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ Worker {worker_id} kritik hata - Issue {issue_id}: {e}")
        return {
            'issue_id': issue_id,
            'results': [],
            'execution_time': execution_time,
            'worker_id': worker_id,
            'error': str(e)
        }

class All65IssuesParallelTest:
    """Tüm 65 issue için paralel test sistemi"""
    
    def __init__(self, max_workers: int = 14):
        self.max_workers = max_workers
        self.test_results = {}
        
    def create_all_test_cases(self) -> dict:
        """Tüm 65 issue için test case'leri oluştur"""
        return {
            # İlk 26 Issue (Mevcut)
            1: ["1000 TL", "500 USD", "750 EUR"],
            2: ["25-12-2023", "2023/12/25", "01.01.2024"],
            3: ["Good quality", "İyi kalite", "Excellent service"],
            4: ["TR", "Turkey", "Türkiye"],
            5: ["+90 555 123 45 67", "0555 123 45 67", "**********"],
            6: ["Sokak No, Şehir, Ülke", "Şehir, Sokak Adı, Ülke", "İstanbul"],
            7: ["Elektronik Kart", "Electronic Card", "Kart Elektronik"],
            8: ["UTC 10:00", "GMT*****:00", "EEST 14:00"],
            9: ["10 kg", "10000 g", "10 kilogram"],
            10: ["12345678901", "123 456 789 01", "123-456-789-01"],
            11: ["1,000.50", "1000,50", "2.500,75"],
            12: ["Toptan", "Bayi", "Distribütör"],
            13: ["2024-01-15", "15-01-2024", "15/01/2024"],
            14: ["1000 TL KDV dahil", "500 USD vergi hariç", "750 EUR + KDV"],
            15: ["ABC Ltd.", "ABC Limited", "ABC Ltd. Şti."],
            16: ["UTC 10:00", "GMT*****:00", "TRT 14:00"],
            17: ["PRD-001", "PRD001", "prd-001"],
            18: ["5 adet", "10 piece", "3 kutu"],
            19: ["50 çalışan", "KOBİ", "Büyük Firma"],
            20: ["1000 USD (kur)", "750 EUR + kur", "2000 TL kuru"],
            21: ["ödeme koşulları esnektir", "ödeme 30 gün içerisinde yapılır", "Net 30 gün"],
            22: ["yıllık 100000 TL", "aylık 50000 USD", "tek seferlik 25000 EUR"],
            23: ["%10 indirim", "500 TL indirim", "1000 TL"],
            24: ["Beyaz Eşya", "Ev Aletleri", "White Goods"],
            25: ["Kredi Kartı", "Banka Transferi", "Açık Hesap"],
            26: ["ürün bazında fatura", "hizmet bazında fatura", "product based"],
            
            # Yeni Issue'lar (27-65)
            27: ["5 gün teslimat", "3 days delivery", "2 hafta"],
            28: ["A. Kaya", "Ali K.", "SR-123"],
            29: ["Q1 hedef", "annual target", "monthly"],
            30: ["100 pieces", "50 koli", "10 pallet"],
            31: ["Fatura: 01.01.2024, Ödeme: 15.01.2024"],
            32: ["1", "Yüksek risk", "Medium"],
            33: ["Sanayi/Toptan", "Sağlık & Teknoloji"],
            34: ["ABC Şirketi ABC Şirketi", "XYZ Ltd. XYZ Limited"],
            35: ["10% off", "500 TL discount"],
            36: ["new product", "mature", "developing"],
            37: ["50 TL shipping", "free shipping"],
            38: ["6 months support", "yarım yıl"],
            39: ["technical support", "service"],
            40: ["<EMAIL>", "**********"],
            41: ["İstanbul Fiyatı: 100 TL", "Ankara price: 90 TL"],
            42: ["yazım hatası", "spelling error"],  # Mevcut
            43: ["Miktar kolonunda 100 TL", "Fiyat kolonunda 5 adet"],
            44: ["tarih eksik", "date missing"],  # Mevcut
            45: ["sayı123metin", "text456number"],  # Mevcut
            46: ["çoklu;bilgi|hücre", "multiple;data|cell"],  # Mevcut
            47: ["karışık123veri", "mixed456data"],  # Mevcut
            48: ["=SUM(A1:A10)", "#DIV/0! error"],  # Formül hataları
            49: ["100 TL", "Price: $50"],  # Fiyat bilgisi
            50: ["Online satış", "Telefon"],  # Satış kanal
            51: ["Teslim edildi", "Delivered"],  # Teslimat durum
            52: ["Ödeme tamamlandı", "Paid"],  # Ödeme durum
            53: ["abc123", "A1234B"],
            54: ["12345", "inv-123"],
            55: ["123456789", "1234567890"],
            56: ["10%", "100 TL"],
            57: ["18% VAT", "KDV 8"],
            58: ["proforma", "final invoice"],
            59: ["ABC Ltd. / ABC Limited", "XYZ Şti. XYZ"],
            60: ["cancelled", "return", "void"],
            61: ["2024-01-01", "Excel format"],
            62: ["Customer Name", "Müşteriİsim"],
            63: ["İSTANBUL", "İst."],
            64: ["N/A", "NULL", ""],
            65: ["Ã¼rÃ¼n", "Ä°stanbul"]
        }
    
    def run_all_parallel_tests(self):
        """Tüm 65 issue'yu paralel test et"""
        print("🚀 TÜM 65 ISSUE PARALEL TEST BAŞLIYOR")
        print("=" * 70)
        print(f"⚙️ Worker sayısı: {self.max_workers}")
        print(f"💻 CPU core sayısı: {mp.cpu_count()}")
        print(f"🕐 Başlama zamanı: {datetime.now()}")
        
        start_time = time.time()
        
        # Test case'leri hazırla
        test_cases = self.create_all_test_cases()
        
        # Worker argümanlarını hazırla
        worker_args = []
        for issue_id, cases in test_cases.items():
            worker_args.append((issue_id, cases, f"W{issue_id}"))
        
        total_test_cases = sum(len(cases) for cases in test_cases.values())
        print(f"📊 Toplam {len(worker_args)} issue, {total_test_cases} test case")
        print(f"⚡ {self.max_workers} worker ile MAKSIMUM paralel işlem!")
        print(f"🔥 Issue/Worker: {len(worker_args)/self.max_workers:.1f}")

        # Process pool ile paralel çalıştır
        try:
            with mp.Pool(processes=self.max_workers) as pool:
                print(f"🚀 {self.max_workers} process başlatılıyor...")
                print(f"💻 Sistem: {mp.cpu_count()} core, {self.max_workers} worker = %{(self.max_workers/mp.cpu_count())*100:.0f} CPU kullanımı")

                # Paralel çalıştır
                results = pool.map(worker_test_issue, worker_args)
                
                # Sonuçları topla
                for result in results:
                    if result and 'issue_id' in result:
                        self.test_results[result['issue_id']] = result
                
        except Exception as e:
            print(f"❌ Process pool hatası: {e}")
            return
        
        total_time = time.time() - start_time
        print(f"⏱️ Toplam paralel süre: {total_time:.2f} saniye")
        
        # Sonuçları analiz et
        self.analyze_all_results()

    def analyze_all_results(self):
        """Tüm 65 issue sonuçlarını analiz et"""
        print(f"\n📊 TÜM 65 ISSUE PARALEL TEST SONUÇLARI")
        print("=" * 70)

        total_tests = 0
        total_detected = 0
        total_fixed = 0
        perfect_issues = 0

        # Issue bazında analiz
        print(f"📋 Issue Bazında Sonuçlar:")
        for issue_id in sorted(self.test_results.keys()):
            result = self.test_results[issue_id]

            if 'error' in result:
                print(f"Issue {issue_id:2d}: ❌ HATA - {result.get('error', 'Bilinmeyen hata')}")
                continue

            issue_results = result['results']
            issue_total = len(issue_results)
            issue_detected = sum(1 for r in issue_results if r['detected'])
            issue_fixed = sum(1 for r in issue_results if r['fixed'])

            detection_rate = (issue_detected / issue_total) * 100 if issue_total > 0 else 0
            fix_rate = (issue_fixed / issue_total) * 100 if issue_total > 0 else 0

            status = "✅" if detection_rate == 100 and fix_rate == 100 else "❌"
            if detection_rate == 100 and fix_rate == 100:
                perfect_issues += 1

            print(f"Issue {issue_id:2d}: {status} Tespit: {detection_rate:5.1f}% | Düzeltme: {fix_rate:5.1f}% | "
                  f"Tests: {issue_total} | Süre: {result['execution_time']:.1f}s")

            total_tests += issue_total
            total_detected += issue_detected
            total_fixed += issue_fixed

        # Genel istatistikler
        overall_detection = (total_detected / total_tests) * 100 if total_tests > 0 else 0
        overall_fix = (total_fixed / total_tests) * 100 if total_tests > 0 else 0

        print(f"\n📊 Genel İstatistikler:")
        print(f"   📝 Toplam test: {total_tests}")
        print(f"   🔍 Toplam tespit: {total_detected} ({overall_detection:.1f}%)")
        print(f"   🔧 Toplam düzeltme: {total_fixed} ({overall_fix:.1f}%)")
        print(f"   🏆 Mükemmel issue'lar: {perfect_issues}/{len(self.test_results)}")

        # Paralel performans
        total_execution_time = sum(r.get('execution_time', 0) for r in self.test_results.values())
        avg_time_per_test = total_execution_time / total_tests if total_tests > 0 else 0

        print(f"\n⚡ SÜPER Paralel Performans:")
        print(f"   Ortalama test süresi: {avg_time_per_test:.3f} saniye")
        print(f"   Toplam işlem süresi: {total_execution_time:.2f} saniye")
        print(f"   🔥 Paralel hızlanma: ~{self.max_workers}x (14 worker!)")
        print(f"   💻 CPU kullanımı: %{(self.max_workers/mp.cpu_count())*100:.0f}")

        # Başarı değerlendirmesi
        all_perfect = perfect_issues == len(self.test_results)
        overall_perfect = overall_detection == 100 and overall_fix == 100

        print(f"\n🏆 FINAL DEĞERLENDİRME:")
        if all_perfect and overall_perfect:
            print(f"   🎉 TÜM 65 ISSUE %100 BAŞARI! (von.md tamamen implement edildi!)")
        else:
            print(f"   ⚠️ Bazı issue'lar %100'e çıkarılmalı")
            failed_issues = []
            for issue_id, result in self.test_results.items():
                if 'error' not in result:
                    issue_results = result['results']
                    issue_total = len(issue_results)
                    issue_detected = sum(1 for r in issue_results if r['detected'])
                    issue_fixed = sum(1 for r in issue_results if r['fixed'])
                    detection_rate = (issue_detected / issue_total) * 100
                    fix_rate = (issue_fixed / issue_total) * 100

                    if detection_rate != 100 or fix_rate != 100:
                        failed_issues.append(issue_id)
                        print(f"      - Issue {issue_id}: Tespit {detection_rate:.1f}%, Düzeltme {fix_rate:.1f}%")

            print(f"\n🔧 Düzeltilmesi Gereken Issue'lar: {failed_issues}")

        print(f"\n🎯 von.md Implementation Durumu:")
        print(f"   📋 Toplam von.md problemleri: 65")
        print(f"   ✅ Implement edilen: {len(self.test_results)}")
        print(f"   🏆 Mükemmel çalışan: {perfect_issues}")
        print(f"   📈 Implementation başarısı: {(len(self.test_results)/65)*100:.1f}%")
        print(f"   🎉 Kalite başarısı: {(perfect_issues/65)*100:.1f}%")

if __name__ == "__main__":
    # Tüm 65 issue'yu paralel test et
    parallel_test = All65IssuesParallelTest(max_workers=14)
    parallel_test.run_all_parallel_tests()
