#!/usr/bin/env python3
"""
Issue 48 Test - Müşteri Ad<PERSON> Standart
von.md'ye göre: Müşteri adres bilgilerinde standart eksikliği
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_48():
    """Issue 48'i kapsamlı test et"""
    
    print("🧪 Issue 48 Test - Müşteri Adres Standart")
    print("=" * 50)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # Küçük harf adresler
        "istanbul kadikoy",
        "ankara cankaya",
        "izmir bornova",
        
        # <PERSON><PERSON><PERSON> virgül adresler
        "Ankara Çankaya Mahallesi",
        "İzmir Bornova No:123",
        "Antalya Muratpaşa",
        
        # Karışık formatlar
        "<PERSON>urs<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Atatürk Cad.",
        "İstanbul Beşiktaş Barbaros Bulvarı No:45",
        "Adana Seyhan <PERSON>",
        
        # <PERSON><PERSON><PERSON><PERSON> adresler
        "İstanbul, Kadı<PERSON>öy, Moda <PERSON>, Caferağa Sok. No:15",
        "Ankara, Çankaya, Kızılay, Atatürk Bulvarı No:100",
        
        # Doğru formatlar (kontrol)
        "İstanbul, Kadıköy",
        "Ankara, Çankaya"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 48 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 48]
            
            # Doğru formatlar için tespit edilmemesi normal
            correct_formats = ["İstanbul, Kadıköy", "Ankara, Çankaya"]
            is_correct_format = test_case in correct_formats
            
            if is_correct_format and not issue_detected:
                success_count += 1
                print(f"   ✅ BAŞARILI: Doğru format tespit edilmedi (normal)")
            elif issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 48 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 48 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_48()
