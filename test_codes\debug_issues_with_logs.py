#!/usr/bin/env python3
"""
Kalan 4 Issue'yu Detaylı Debug Et - LOG DOSYASI İLE
Issue 21, 23, 24, 25 i<PERSON>in detaylı log ve analiz
Hem ekranda hem dosyada log tutar
"""

from component_cards import DataQualityProcessor, OllamaLLMClient
import datetime
import os

# Global log dosyası
log_file = None

def log_message(message):
    """Mesajı hem ekrana hem dosyaya yaz"""
    print(message)
    if log_file:
        log_file.write(message + "\n")
        log_file.flush()

def debug_issue(issue_id, test_cases, issue_name):
    """Bir issue'yu detaylı debug et"""
    
    log_message(f"\n🔍 Issue {issue_id} ({issue_name}) Debug")
    log_message("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        log_message(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü - DETAYLI LOG
            log_message(f"   🔍 LLM'e gönderilen prompt analizi...")
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = issue_id in detected_issues
            
            log_message(f"   📊 Tespit edilen tüm issue'lar: {detected_issues}")
            log_message(f"   🎯 Issue {issue_id} tespit edildi mi: {'✅ EVET' if issue_detected else '❌ HAYIR'}")
            
            # 2. Process_value ile tam test - DETAYLI LOG
            if issue_detected:
                log_message(f"   🔧 Düzeltme işlemi başlatılıyor...")
                processed_value, fixes = processor.process_value(test_case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                log_message(f"   📋 Tüm düzeltmeler: {len(fixes)} adet")
                log_message(f"   🎯 Issue {issue_id} düzeltmeleri: {len(issue_fixes)} adet")
                
                if issue_fixes:
                    log_message(f"   ✅ BAŞARILI: '{processed_value}'")
                    for fix in issue_fixes:
                        log_message(f"      🔧 {fix['original']} → {fix['fixed']}")
                    success_count += 1
                else:
                    log_message(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                    log_message(f"   🔍 İşlenmiş değer: '{processed_value}'")
            else:
                log_message(f"   ❌ Tespit edilmediği için düzeltme yapılmadı")
                
        except Exception as e:
            log_message(f"   ❌ HATA: {e}")
            import traceback
            log_message(f"   📋 Detaylı hata: {traceback.format_exc()}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    log_message(f"\n📊 Issue {issue_id} Debug Sonuçları:")
    log_message(f"   📝 Toplam test: {total_tests}")
    log_message(f"   ✅ Başarılı: {success_count}")
    log_message(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    return success_rate

def main():
    """Ana debug fonksiyonu"""
    
    global log_file
    
    # Log dosyası oluştur
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"debug_issues_{timestamp}.log"
    log_file = open(log_filename, 'w', encoding='utf-8')
    
    try:
        log_message("🐛 KALAN 4 ISSUE DEBUG ANALİZİ - LOG DOSYASI İLE")
        log_message("=" * 80)
        log_message(f"📝 Log dosyası: {log_filename}")
        
        # Test case'leri - Paralel test'ten alınan gerçek veriler
        test_data = {
            21: {
                "name": "Sözleşme Koşulları",
                "cases": [
                    "ödeme koşulları esnektir",
                    "ödeme 30 gün içerisinde yapılır", 
                    "Net 30 gün",
                    "Peşin ödeme",
                    "esnek ödeme",
                    "30 günde ödeme"
                ]
            },
            23: {
                "name": "Kampanya İndirim",
                "cases": [
                    "%10 indirim",
                    "500 TL indirim",
                    "10 percent off",
                    "500 TL off",
                    "%15",
                    "1000 TL"
                ]
            },
            24: {
                "name": "Ürün Kategori Standart",
                "cases": [
                    "Beyaz Eşya",
                    "Ev Aletleri",
                    "White Goods",
                    "Home Appliances",
                    "Küçük Ev Aletleri",
                    "Büyük Beyaz Eşya"
                ]
            },
            25: {
                "name": "Ödeme Türleri",
                "cases": [
                    "Kredi Kartı",
                    "Banka Transferi",
                    "Credit Card",
                    "Bank Transfer",
                    "Nakit",
                    "Cash"
                ]
            }
        }
        
        results = {}
        
        for issue_id, data in test_data.items():
            rate = debug_issue(issue_id, data["cases"], data["name"])
            results[issue_id] = rate
        
        log_message(f"\n🎯 GENEL DEBUG SONUÇLARI:")
        log_message("=" * 50)
        for issue_id, rate in results.items():
            status = "✅" if rate == 100.0 else "❌"
            log_message(f"   {status} Issue {issue_id}: {rate:.1f}%")
        
        total_success = sum(1 for rate in results.values() if rate == 100.0)
        log_message(f"\n🏆 {total_success}/4 issue %100 başarıda!")
        
        log_message(f"\n📝 Detaylı log dosyası: {log_filename}")
        
        return results
        
    finally:
        if log_file:
            log_file.close()

if __name__ == "__main__":
    main()
