#!/usr/bin/env python3
"""
Issue 42 Test - <PERSON><PERSON><PERSON><PERSON>alar<PERSON>
von.md'ye göre: El Yazımı Hataları ve Yazım Yanlışları
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_42():
    """Issue 42'yi kapsamlı test et"""
    
    print("🧪 Issue 42 Test - Yazım Hataları")
    print("=" * 50)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # von.md'deki örnek
        "Elktronik Kart",
        "Elektronik Kart",  # Do<PERSON><PERSON> yazım (kontrol)
        
        # Yaygın yazım hataları
        "müsteri bilgisi",
        "fatüra detayı", 
        "urün kodu",
        "sirket adı",
        "müştri geri bildirimi",
        "elektronk cihaz",
        "krat numarası",
        "kard okuyucu",
        
        # <PERSON><PERSON><PERSON> (kontrol)
        "müşteri bilgisi",
        "fatura detayı",
        "ürün kodu",
        "şirket adı",
        
        # Karışık yazım hataları
        "Elktronik Krat Okuyucu",
        "Müsteri Fatüra Bilgisi",
        "Urün Sirket Kodu"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 42 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 42]
            
            # Doğru yazımlar için tespit edilmemesi normal
            correct_spellings = ["Elektronik Kart", "müşteri bilgisi", "fatura detayı", "ürün kodu", "şirket adı"]
            is_correct_spelling = test_case in correct_spellings
            
            if is_correct_spelling and not issue_detected:
                success_count += 1
                print(f"   ✅ BAŞARILI: Doğru yazım tespit edilmedi (normal)")
            elif issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 42 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 42 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_42()
