#!/usr/bin/env python3
"""
Issue 10 (<PERSON><PERSON> Numarası Format) LLM İyileştirme Testi
Von.md'ye uygun test case'leri ile %100 başarı hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_10_improved():
    """Issue 10'u LLM iyileştirmesi ile test et"""
    
    print("🧪 Issue 10 (Kimlik Numarası Format) LLM İyileştirme Testi")
    print("=" * 60)
    print("🎯 Hedef: %100 başarı - Von.md'ye uygun test case'leri")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Von.md'ye uygun test case'leri - Kimlik numarası format farklılıkları
    test_cases = [
        "12345678901",           # Standart format
        "123 456 789 01",        # Boşluklu
        "123-456-789-01",        # Tireli
        "123.456.789.01",        # Noktalı
        "12345 678901",          # <PERSON><PERSON><PERSON><PERSON> boşluk
        "123 45 678 90 1"        # Çok boşluklu
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_10_detected = 10 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_10_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_10_fixes = [f for f in fixes if f['issue_id'] == 10]
            
            if issue_10_detected and issue_10_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_10_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif not issue_10_detected and test_case == "12345678901":
                # Standart format tespit edilmemeli
                success_count += 1
                print(f"   ✅ DOĞRU: Standart format tespit edilmedi")
            elif issue_10_detected and not issue_10_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_10_detected}, Düzeltme={len(issue_10_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 10 Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 Issue 10 %100 BAŞARI!")
        return True
    else:
        print(f"   ⚠️ Issue 10 henüz %100 değil")
        return False

if __name__ == "__main__":
    success = test_issue_10_improved()
    
    if success:
        print("\n🎯 Issue 10 %100 başarıya ulaştı!")
        print("   ➡️ Issue 11'e geçilebilir")
    else:
        print("\n⚠️ Issue 10 daha fazla iyileştirme gerekiyor")
        print("   ➡️ LLM prompt'larını güçlendir")
