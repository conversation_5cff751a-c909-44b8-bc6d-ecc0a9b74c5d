#!/usr/bin/env python3
"""
Issue 31 Test - Fatura/Ödeme <PERSON>lık
von.md'ye göre: Fatura tarihleri ve ödeme tarihleri aynı kolon içinde bulunabilir
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_31():
    """Issue 31'i kapsamlı test et"""
    
    print("🧪 Issue 31 Test - Fatura/Ödeme Tarih Tu<PERSON>ızlık")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # Temel format
        "Fatura: 01.01.2024, Ödeme: 15.01.2024",
        "Fatura Tarihi: 01-01-2024, Ödeme Ta<PERSON>hi: 10-01-2024",
        
        # İngilizce format
        "Invoice: 2024-01-01, Payment: 2024-01-15",
        "Invoice Date: 01/01/2024, Payment Date: 15/01/2024",
        
        # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ödeme faturadan önce)
        "Fatura: 15.01.2024, Ödeme: 01.01.2024",
        
        # Farklı formatlar
        "fatura tarihi: 01.01.2024, ödeme: 15.01.2024",
        "FATURA: 2024/01/01, ÖDEME: 2024/01/15",
        
        # Karışık format
        "Fatura 01-01-2024 Ödeme 15-01-2024",
        "Invoice: 1 Jan 2024, Payment: 15 Jan 2024",
        
        # Eksik bilgi
        "Fatura: 01.01.2024",
        "Ödeme: 15.01.2024"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 31 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 31]
            
            if issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 31 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 31 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_31()
