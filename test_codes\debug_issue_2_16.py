#!/usr/bin/env python3
"""
Issue 2 ve 16 debug scripti - Düşük performanslı problemleri analiz et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def debug_issue_2():
    """Issue 2 (Tarih format) debug et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Issue 2 test case'leri
    test_cases = [
        # Kolay testler
        "25-12-2023",
        "2023/12/25", 
        "01.01.2024",
        
        # <PERSON>or testler
        "1 Ocak 2024",
        "Jan 1, 2024",
        "2024-12-25T10:30:00",
        "Pazartesi, 1 Ocak 2024",
        "Monday, Jan 1, 2024"
    ]
    
    print("🔍 Issue 2 (Tarih Format) Debug")
    print("=" * 50)
    
    failed_cases = []
    
    for i, test_value in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_2_detected = 2 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_2_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_2_detected:
            # 2. Düzeltme fonksiyonunu test et
            try:
                fixed_result = processor.fix_date_issues(test_value)
                print(f"   🔧 Düzeltme: '{test_value}' → '{fixed_result}'")
                
                # 3. Process_value ile tam test
                processed_value, fixes = processor.process_value(test_value, "test_column")
                
                # Issue 2 düzeltmesi uygulandı mı?
                issue_2_fixes = [f for f in fixes if f['issue_id'] == 2]
                if issue_2_fixes:
                    print(f"   ✅ Process_value düzeltmesi: '{processed_value}'")
                    for fix in issue_2_fixes:
                        print(f"      - {fix['original']} → {fix['fixed']}")
                else:
                    print(f"   ❌ Process_value düzeltmesi uygulanmadı")
                    failed_cases.append((test_value, "Düzeltme uygulanmadı"))
                    
            except Exception as e:
                print(f"   ❌ Düzeltme hatası: {e}")
                failed_cases.append((test_value, f"Hata: {e}"))
        else:
            failed_cases.append((test_value, "Tespit edilmedi"))
    
    print(f"\n❌ Başarısız Test Case'ler:")
    for case, reason in failed_cases:
        print(f"   - '{case}': {reason}")
    
    return failed_cases

def debug_issue_16():
    """Issue 16 (Sözleşme zaman dilimi) debug et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Issue 16 test case'leri
    test_cases = [
        # Kolay testler
        "UTC 10:00",
        "GMT+3 13:00",
        "TRT 14:00",
        
        # Zor testler
        "2024-01-01 10:00:00 UTC",
        "2024-01-01 13:00:00 +03:00",
        "10:30:00 GMT+3",
        "14:00 TRT"
    ]
    
    print("\n🔍 Issue 16 (Sözleşme Zaman Dilimi) Debug")
    print("=" * 50)
    
    failed_cases = []
    
    for i, test_value in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_16_detected = 16 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_16_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_16_detected:
            # 2. Düzeltme fonksiyonunu test et
            try:
                fixed_result = processor.fix_contract_timezone_issues(test_value)
                print(f"   🔧 Düzeltme: '{test_value}' → '{fixed_result}'")
                
                # 3. Process_value ile tam test
                processed_value, fixes = processor.process_value(test_value, "test_column")
                
                # Issue 16 düzeltmesi uygulandı mı?
                issue_16_fixes = [f for f in fixes if f['issue_id'] == 16]
                if issue_16_fixes:
                    print(f"   ✅ Process_value düzeltmesi: '{processed_value}'")
                    for fix in issue_16_fixes:
                        print(f"      - {fix['original']} → {fix['fixed']}")
                else:
                    print(f"   ❌ Process_value düzeltmesi uygulanmadı")
                    failed_cases.append((test_value, "Düzeltme uygulanmadı"))
                    
            except Exception as e:
                print(f"   ❌ Düzeltme hatası: {e}")
                failed_cases.append((test_value, f"Hata: {e}"))
        else:
            failed_cases.append((test_value, "Tespit edilmedi"))
    
    print(f"\n❌ Başarısız Test Case'ler:")
    for case, reason in failed_cases:
        print(f"   - '{case}': {reason}")
    
    return failed_cases

if __name__ == "__main__":
    print("🧪 Issue 2 ve 16 Debug Analizi")
    print("=" * 60)
    
    # Issue 2 debug
    issue_2_failures = debug_issue_2()
    
    # Issue 16 debug  
    issue_16_failures = debug_issue_16()
    
    print(f"\n📊 Özet:")
    print(f"   Issue 2 başarısız: {len(issue_2_failures)}")
    print(f"   Issue 16 başarısız: {len(issue_16_failures)}")
    
    if issue_2_failures or issue_16_failures:
        print(f"\n🔧 İyileştirme gerekiyor!")
    else:
        print(f"\n✅ Tüm testler başarılı!")
