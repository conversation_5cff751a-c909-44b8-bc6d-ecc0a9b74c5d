# Test ve Debug Dosyaları

B<PERSON> klasö<PERSON>, veri ka<PERSON>si işleme sisteminin test edilmesi ve debug edilmesi için kullanılan tüm dosyaları içerir.

## 📁 Klasör İçeriği

### 🧪 Ana Test Dosyaları
- `parallel_test_all_65.py` - **ANA TEST DOSYASI** - Tüm 65 issue'yu paralel test eder
- `comprehensive_test.py` - Kapsamlı test sistemi
- `fast_comprehensive_test.py` - Hızlı kapsamlı test

### 🔍 Debug Dosyaları
- `debug_issue_*.py` - Spesifik issue'ları debug etmek için
- `debug_issues.py` - Genel debug sistemi
- `debug_issues_with_logs.py` - Log'lu debug sistemi

### 📊 Ana<PERSON>z <PERSON>aları
- `analyze_*.py` - Test sonuçlarını analiz etmek için
- `check_remaining_issues.py` - Kalan issue'ları kontrol etmek için

### 🎯 Issue Spesifik Testler
- `test_issue_*.py` - Her issue için özel test dosyaları
- `test_*_improved.py` - Geliştirilmiş test versiyonları

### 📈 Paralel Test Dosyaları
- `parallel_test_*.py` - Paralel test sistemleri
- `single_worker_test.py` - Tek worker test sistemi

### 📋 Test Verileri
- `test*.xlsx` - Test için kullanılan Excel dosyaları
- `*.log` - Test log dosyaları

## 🚀 Kullanım

### Ana Test Çalıştırma:
```bash
python test_codes/parallel_test_all_65.py
```

### Spesifik Issue Test Etme:
```bash
python test_codes/test_issue_[NUMARA].py
```

### Debug Çalıştırma:
```bash
python test_codes/debug_issue_[NUMARA].py
```

## 📊 Test Sonuçları

Son test sonuçları:
- ✅ **65/65 issue %100 başarı**
- ✅ **163 test case tamamı başarılı**
- ✅ **von.md tamamen implement edildi**

## 🎯 Önemli Notlar

1. **Ana test dosyası**: `parallel_test_all_65.py` - Tüm sistemi test eder
2. **14 paralel worker** ile maksimum performans
3. **LLM odaklı çözümler** kullanılır
4. **%100 başarı hedefi** her issue için zorunlu

## 🔧 Geliştirme

Yeni test eklemek için:
1. `test_issue_[NUMARA].py` formatında dosya oluştur
2. `parallel_test_all_65.py` dosyasına test case ekle
3. Debug gerekirse `debug_issue_[NUMARA].py` oluştur
