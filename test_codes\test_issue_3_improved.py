#!/usr/bin/env python3
"""
Issue 3 (<PERSON><PERSON>ılıkları) LLM İyileştirme Testi
Von.md'ye uygun test case'leri ile %100 başarı hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_3_improved():
    """Issue 3'ü LLM iyileştirmesi ile test et"""
    
    print("🧪 Issue 3 (Dil Farklılıkları) LLM İyileştirme Testi")
    print("=" * 60)
    print("🎯 Hedef: %100 başarı - Von.md'ye uygun test case'leri")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Von.md'ye uygun test case'leri - Dil farklılıkları
    test_cases = [
        'Good quality',           # İngilizce
        'İyi kalite',            # Türkçe (kontrol)
        'Excellent service',      # İngilizce
        'Mükemmel hizmet',       # Türkç<PERSON> (kontrol)
        'Customer feedback',      # İngilizce
        'Müşteri geri bildirimi' # Türkçe (kontrol)
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_3_detected = 3 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_3_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_3_fixes = [f for f in fixes if f['issue_id'] == 3]
            
            if issue_3_detected and issue_3_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_3_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif not issue_3_detected and 'İyi' in test_case or 'Mükemmel' in test_case or 'Müşteri' in test_case:
                # Türkçe test case'leri tespit edilmemeli
                success_count += 1
                print(f"   ✅ DOĞRU: Türkçe metin tespit edilmedi")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_3_detected}, Düzeltme={len(issue_3_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 3 Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 Issue 3 %100 BAŞARI!")
        return True
    else:
        print(f"   ⚠️ Issue 3 henüz %100 değil")
        return False

if __name__ == "__main__":
    success = test_issue_3_improved()
    
    if success:
        print("\n🎯 Issue 3 %100 başarıya ulaştı!")
        print("   ➡️ Diğer issue'lara geçilebilir")
    else:
        print("\n⚠️ Issue 3 daha fazla iyileştirme gerekiyor")
        print("   ➡️ LLM prompt'larını güçlendir")
