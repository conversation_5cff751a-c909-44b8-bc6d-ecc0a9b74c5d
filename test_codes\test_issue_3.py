#!/usr/bin/env python3
"""
Issue 3 Test - Dil Standardizasyonu
von.md'ye göre: Dil standardizasyonu (İngilizce/Türkçe karışık)
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_3():
    """Issue 3'ü kapsamlı test et"""
    
    print("🧪 Issue 3 Test - Dil Standardizasyonu")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # İngilizce-Türkçe karışık
        "Product Name",
        "Customer Info",
        "Order Date",
        "Payment Method",
        "Shipping Address",
        "Total Amount",
        "Status Active",
        "User Profile",
        "Category Electronics",
        "Brand Apple",
        
        # Türkçe-İngilizce karışık
        "Ürün Name",
        "Müşteri Info",
        "Sipariş Date",
        "Ödeme Method",
        "Teslimat Address",
        "Toplam Amount",
        "Durum Active",
        "Kullanıcı Profile",
        "Kategori Electronics",
        "Marka Apple",
        
        # Tamamen İngilizce (düzeltilmeli)
        "Product",
        "Customer",
        "Order",
        "Payment",
        "Shipping",
        "Total",
        "Status",
        "User",
        "Category",
        "Brand",
        
        # Doğru format (kontrol)
        "Ürün Adı",
        "Müşteri Bilgisi",
        "Sipariş Tarihi"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 3 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 3]
            
            # Doğru formatlar için tespit edilmemesi normal
            correct_formats = ["Ürün Adı", "Müşteri Bilgisi", "Sipariş Tarihi"]
            is_correct_format = test_case in correct_formats
            
            if is_correct_format and not issue_detected:
                success_count += 1
                print(f"   ✅ BAŞARILI: Doğru format tespit edilmedi (normal)")
            elif issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 3 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 3 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_3()
