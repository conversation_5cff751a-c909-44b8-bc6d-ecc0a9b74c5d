#!/usr/bin/env python3
"""
Paralel Test'teki <PERSON> Test Case'leri ile Test
Issue 21 ve 25 için paralel test'teki aynı test case'leri kullan
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_exact_parallel_cases():
    """Paralel test'teki tam test case'leri ile test et"""
    
    print("🧪 Paralel Test'teki Tam Test Case'leri ile Test")
    print("=" * 70)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Paralel test'teki TAM test case'leri
    test_data = {
        21: {
            "name": "Sözleşme Koşulları",
            "cases": ["ödeme koşulları esnektir", "ödeme 30 gün içerisinde yapılır", "Net 30 gün", "Peşin ödeme", "esnek ödeme", "30 günde ödeme"]
        },
        25: {
            "name": "Ödeme Türleri", 
            "cases": ["Kredi <PERSON>", "Banka Transferi", "<PERSON>çık <PERSON>p", "Credit Card", "Bank Transfer", "Open Account"]
        }
    }
    
    results = {}
    
    for issue_id, data in test_data.items():
        print(f"\n🔍 Issue {issue_id} ({data['name']}) Test")
        print("=" * 50)
        
        success_count = 0
        total_tests = len(data['cases'])
        
        for i, test_case in enumerate(data['cases'], 1):
            print(f"\n📝 Test {i}: '{test_case}'")
            
            try:
                # 1. Tespit kontrolü
                detected_issues = processor.detect_issues(test_case, "test_column")
                issue_detected = issue_id in detected_issues
                
                print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
                
                # 2. Process_value ile tam test
                processed_value, fixes = processor.process_value(test_case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                if issue_detected and issue_fixes:
                    success_count += 1
                    print(f"   ✅ BAŞARILI: '{processed_value}'")
                    for fix in issue_fixes:
                        print(f"      🔧 {fix['original']} → {fix['fixed']}")
                elif issue_detected and not issue_fixes:
                    print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                else:
                    print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                    
            except Exception as e:
                print(f"   ❌ HATA: {e}")
        
        # Sonuçları analiz et
        success_rate = (success_count / total_tests) * 100
        results[issue_id] = success_rate
        
        print(f"\n📊 Issue {issue_id} Sonuçları:")
        print(f"   📝 Toplam test: {total_tests}")
        print(f"   ✅ Başarılı: {success_count}")
        print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    print(f"\n🎯 GENEL SONUÇLAR:")
    print("=" * 40)
    for issue_id, rate in results.items():
        status = "✅" if rate == 100.0 else "❌"
        print(f"   {status} Issue {issue_id}: {rate:.1f}%")
    
    total_success = sum(1 for rate in results.values() if rate == 100.0)
    print(f"\n🏆 {total_success}/2 issue %100 başarıda!")
    
    return results

if __name__ == "__main__":
    test_exact_parallel_cases()
