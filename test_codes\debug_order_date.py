#!/usr/bin/env python3
"""
Sipariş tarih format düzeltme debug scripti
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def debug_order_date_fix():
    """Sipariş tarih düzeltme fonksiyonunu debug et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri
    test_cases = [
        "Pazartesi, 1 Ocak 2024",
        "Monday, Jan 1, 2024", 
        "01-Jan-2024",
        "Salı, 15 Şubat 2024"
    ]
    
    print("🔍 Sipariş Tarih Format Düzeltme Debug")
    print("=" * 50)
    
    for test_value in test_cases:
        print(f"\n📝 Test: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        print(f"   Tespit edilen sorunlar: {detected_issues}")
        
        # 2. Issue 13 tespit edildi mi?
        if 13 in detected_issues:
            print("   ✅ Issue 13 tespit edildi")
            
            # 3. Düzeltme fonksiyonunu direkt çağır
            try:
                fixed_result = processor.fix_order_date_issues(test_value)
                print(f"   🔧 Düzeltme sonucu: '{fixed_result}'")
                
                # 4. Sonuç değişti mi?
                if fixed_result != test_value:
                    print("   ✅ Değer değişti")
                else:
                    print("   ❌ Değer değişmedi")
                    
            except Exception as e:
                print(f"   ❌ Düzeltme hatası: {e}")
                
        else:
            print("   ❌ Issue 13 tespit edilmedi")
        
        # 5. Process_value ile tam test
        try:
            processed_value, fixes = processor.process_value(test_value, "test_column")
            print(f"   🔄 Process_value sonucu: '{processed_value}'")
            print(f"   🔧 Uygulanan düzeltmeler: {len(fixes)}")
            
            # Issue 13 düzeltmesi uygulandı mı?
            issue_13_fixes = [f for f in fixes if f['issue_id'] == 13]
            if issue_13_fixes:
                print("   ✅ Issue 13 düzeltmesi uygulandı")
                for fix in issue_13_fixes:
                    print(f"      - {fix['old_value']} -> {fix['new_value']}")
            else:
                print("   ❌ Issue 13 düzeltmesi uygulanmadı")
                
        except Exception as e:
            print(f"   ❌ Process_value hatası: {e}")

if __name__ == "__main__":
    debug_order_date_fix()
