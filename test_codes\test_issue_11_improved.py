#!/usr/bin/env python3
"""
Issue 11 (On<PERSON><PERSON>k İşaret) LLM İyileştirme Testi
Von.md'ye uygun test case'leri ile %100 başarı hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_11_improved():
    """Issue 11'i LLM iyileştirmesi ile test et"""
    
    print("🧪 Issue 11 (Ondalık İşaret) LLM İyileştirme Testi")
    print("=" * 60)
    print("🎯 Hedef: %100 başarı - Von.md'ye uygun test case'leri")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Von.md'ye uygun test case'leri - Fiyat ondalık işaret farklılıkları
    test_cases = [
        "1,000.50",              # Amerikan formatı
        "1000,50",               # Avrupa formatı
        "2.500,75",              # Karışık format
        "3000.25",               # Standart
        "1,500.00",              # <PERSON><PERSON><PERSON><PERSON><PERSON> ondalık
        "2500,50"                # Ba<PERSON><PERSON> virgül
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_11_detected = 11 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_11_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_11_fixes = [f for f in fixes if f['issue_id'] == 11]
            
            if issue_11_detected and issue_11_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_11_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif not issue_11_detected and ("3000.25" in test_case):
                # Standart format tespit edilmemeli
                success_count += 1
                print(f"   ✅ DOĞRU: Standart format tespit edilmedi")
            elif issue_11_detected and not issue_11_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_11_detected}, Düzeltme={len(issue_11_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 11 Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 Issue 11 %100 BAŞARI!")
        return True
    else:
        print(f"   ⚠️ Issue 11 henüz %100 değil")
        return False

if __name__ == "__main__":
    success = test_issue_11_improved()
    
    if success:
        print("\n🎯 Issue 11 %100 başarıya ulaştı!")
        print("   ➡️ Issue 17'ye geçilebilir")
    else:
        print("\n⚠️ Issue 11 daha fazla iyileştirme gerekiyor")
        print("   ➡️ Düzeltme fonksiyonunu güçlendir")
