#!/usr/bin/env python3
"""
Hızlı Paralel Test - 8 Worker ile Issue 1-10 Test
"""

import multiprocessing as mp
import time
from datetime import datetime

def worker_test_issue(args):
    """Worker fonksiyonu - Her process kendi LLM client'ı"""
    issue_id, test_cases, worker_id = args
    
    # Her worker kendi import'unu yapar (process isolation)
    from component_cards import DataQualityProcessor, OllamaLLMClient
    
    print(f"🚀 Worker {worker_id} başladı - Issue {issue_id}")
    
    results = []
    start_time = time.time()
    
    try:
        # Her worker kendi LLM client'ı oluşturur
        llm_client = OllamaLLMClient()
        processor = DataQualityProcessor(llm_client)
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                # Tespit
                detected_issues = processor.detect_issues(test_case, "test_column")
                detected = issue_id in detected_issues
                
                # Düzeltme (sadece tespit edilirse)
                fixed = False
                if detected:
                    _, fixes = processor.process_value(test_case, "test_column")
                    issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                    fixed = len(issue_fixes) > 0
                
                results.append({
                    'test_case': test_case,
                    'detected': detected,
                    'fixed': fixed,
                    'worker_id': worker_id
                })
                
                # Progress (her 2 testte bir)
                if i % 2 == 0:
                    print(f"   Worker {worker_id}: {i}/{len(test_cases)} tamamlandı")
                    
            except Exception as e:
                print(f"   ❌ Worker {worker_id} test hatası ({test_case}): {e}")
                results.append({
                    'test_case': test_case,
                    'detected': False,
                    'fixed': False,
                    'worker_id': worker_id,
                    'error': str(e)
                })
        
        execution_time = time.time() - start_time
        print(f"✅ Worker {worker_id} tamamlandı - Issue {issue_id} - {execution_time:.1f}s")
        
        return {
            'issue_id': issue_id,
            'results': results,
            'execution_time': execution_time,
            'worker_id': worker_id
        }
        
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ Worker {worker_id} kritik hata - Issue {issue_id}: {e}")
        return {
            'issue_id': issue_id,
            'results': [],
            'execution_time': execution_time,
            'worker_id': worker_id,
            'error': str(e)
        }

class QuickParallelTest:
    """Hızlı paralel test sistemi"""
    
    def __init__(self, max_workers: int = 8):
        # 8 worker ile hızlı test
        self.max_workers = min(max_workers, mp.cpu_count() - 2)
        self.test_results = {}
        
    def create_quick_test_cases(self) -> dict:
        """Hızlı test için Issue 1-10"""
        return {
            1: ["1000 TL", "500 USD", "€ 1000"],
            2: ["25-12-2023", "2023/12/25", "1 Ocak 2024"],
            3: ["Hello Merhaba", "Customer müşteri", "Product ürün"],
            4: ["Turkey", "İstanbul", "İstanbul, Turkey"],
            5: ["+90 555 123 45 67", "0555 123 45 67", "5551234567"],
            6: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            7: ["Atatürk Cad. No:123", "Atatürk Caddesi 123", "Ataturk Cd. 123/A"],
            8: ["ABC Ltd.", "ABC Limited", "ABC A.Ş."],
            9: ["PRD-001", "PRD001", "prd-001"],
            10: ["10 kg", "10kg", "10 kilogram"]
        }
    
    def run_quick_test(self):
        """Hızlı paralel test çalıştır"""
        print("⚡ Hızlı Paralel Test Başlıyor")
        print("=" * 60)
        print(f"⚙️ Worker sayısı: {self.max_workers}")
        print(f"💻 CPU core sayısı: {mp.cpu_count()}")
        print(f"🎯 Test kapsamı: Issue 1-10 (Hızlı test)")
        print(f"🕐 Başlama zamanı: {datetime.now()}")
        
        start_time = time.time()
        
        # Test case'leri hazırla
        test_cases = self.create_quick_test_cases()
        
        # Worker argümanlarını hazırla
        worker_args = []
        for issue_id, cases in test_cases.items():
            worker_args.append((issue_id, cases, f"W{issue_id}"))
        
        total_test_cases = sum(len(cases) for cases in test_cases.values())
        print(f"📊 Toplam {len(worker_args)} issue, {total_test_cases} test case")
        print(f"⚡ {self.max_workers} worker ile paralel işlem")
        
        # Process pool ile paralel çalıştır
        try:
            with mp.Pool(processes=self.max_workers) as pool:
                print(f"🔄 {self.max_workers} process başlatılıyor...")
                print(f"💻 Sistem: {mp.cpu_count()} core, {self.max_workers} worker kullanılıyor")
                
                # Paralel çalıştır
                results = pool.map(worker_test_issue, worker_args)
                
                # Sonuçları topla
                for result in results:
                    if result and 'issue_id' in result:
                        self.test_results[result['issue_id']] = result
                
        except Exception as e:
            print(f"❌ Process pool hatası: {e}")
            return
        
        total_time = time.time() - start_time
        print(f"⏱️ Toplam paralel süre: {total_time:.2f} saniye")
        
        # Sonuçları analiz et
        self.analyze_quick_results()
    
    def analyze_quick_results(self):
        """Hızlı test sonuçlarını analiz et"""
        print(f"\n📊 HIZLI PARALEL TEST SONUÇLARI")
        print("=" * 60)
        
        total_tests = 0
        total_detected = 0
        total_fixed = 0
        perfect_issues = 0
        
        # Issue bazında analiz
        print(f"📋 Issue Bazında Sonuçlar:")
        for issue_id in sorted(self.test_results.keys()):
            result = self.test_results[issue_id]
            
            if 'error' in result:
                print(f"Issue {issue_id:2d}: ❌ HATA - {result.get('error', 'Bilinmeyen hata')}")
                continue
            
            issue_results = result['results']
            issue_total = len(issue_results)
            issue_detected = sum(1 for r in issue_results if r['detected'])
            issue_fixed = sum(1 for r in issue_results if r['fixed'])
            
            detection_rate = (issue_detected / issue_total) * 100 if issue_total > 0 else 0
            fix_rate = (issue_fixed / issue_total) * 100 if issue_total > 0 else 0
            
            status = "✅" if detection_rate == 100 and fix_rate == 100 else "❌"
            if detection_rate == 100 and fix_rate == 100:
                perfect_issues += 1
            
            print(f"Issue {issue_id:2d}: {status} Tespit: {detection_rate:5.1f}% | Düzeltme: {fix_rate:5.1f}% | "
                  f"Tests: {issue_total} | Süre: {result['execution_time']:.1f}s | Worker: {result['worker_id']}")
            
            total_tests += issue_total
            total_detected += issue_detected
            total_fixed += issue_fixed
        
        # Genel istatistikler
        overall_detection = (total_detected / total_tests) * 100 if total_tests > 0 else 0
        overall_fix = (total_fixed / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n📊 Genel İstatistikler:")
        print(f"   📝 Toplam test: {total_tests}")
        print(f"   🔍 Toplam tespit: {total_detected} ({overall_detection:.1f}%)")
        print(f"   🔧 Toplam düzeltme: {total_fixed} ({overall_fix:.1f}%)")
        print(f"   🏆 Mükemmel issue'lar: {perfect_issues}/{len(self.test_results)}")
        
        # Paralel performans
        total_execution_time = sum(r.get('execution_time', 0) for r in self.test_results.values())
        avg_time_per_test = total_execution_time / total_tests if total_tests > 0 else 0
        
        print(f"\n⚡ Paralel Performans:")
        print(f"   Ortalama test süresi: {avg_time_per_test:.3f} saniye")
        print(f"   Toplam işlem süresi: {total_execution_time:.2f} saniye")
        print(f"   Paralel hızlanma: ~{self.max_workers}x")
        
        # Başarı değerlendirmesi
        all_perfect = perfect_issues == len(self.test_results)
        overall_perfect = overall_detection == 100 and overall_fix == 100
        
        print(f"\n🏆 Hızlı Test Değerlendirmesi:")
        if all_perfect and overall_perfect:
            print(f"   🎉 TÜM ISSUE'LAR %100 BAŞARI! (8 worker paralel test başarılı)")
            print(f"   ✅ Tam paralel test için hazır!")
        else:
            print(f"   ⚠️ Bazı issue'lar %100'e çıkarılmalı")

if __name__ == "__main__":
    # Hızlı paralel test çalıştır
    quick_test = QuickParallelTest(max_workers=8)
    quick_test.run_quick_test()
