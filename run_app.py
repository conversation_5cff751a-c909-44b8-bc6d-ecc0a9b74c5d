#!/usr/bin/env python3
"""
LLM Veri Ön İşleme Uygulaması Çalıştırıcı
"""

import subprocess
import sys
import os
import requests
from pathlib import Path

def check_requirements():
    """Gereksinimleri kontrol et"""
    print("🔍 Gereksinimler kontrol ediliyor...")
    
    required_packages = [
        'streamlit', 'pandas', 'numpy', 'requests', 
        'openpyxl', 'xlrd', 'tqdm', 'aiohttp'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n📦 Eksik paketler yükleniyor: {', '.join(missing_packages)}")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            *missing_packages
        ])
        print("✅ Tüm paketler yüklendi")
    else:
        print("✅ Tüm gereksinimler mevcut")

def check_ollama():
    """Ollama bağlantısını kontrol et"""
    print("\n🤖 Ollama bağlantısı kontrol ediliyor...")
    
    try:
        from config import OLLAMA_URL, OLLAMA_MODEL
        
        # Ollama sunucu kontrolü
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        if response.status_code == 200:
            print(f"✅ Ollama sunucusu erişilebilir: {OLLAMA_URL}")
            
            # Model kontrolü
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            
            if any(OLLAMA_MODEL in name for name in model_names):
                print(f"✅ Model mevcut: {OLLAMA_MODEL}")
                return True
            else:
                print(f"❌ Model bulunamadı: {OLLAMA_MODEL}")
                print(f"Mevcut modeller: {model_names}")
                
                # Model indirme önerisi
                print(f"\n📥 Model indirmek için:")
                print(f"ollama pull {OLLAMA_MODEL}")
                return False
        else:
            print(f"❌ Ollama sunucusu erişilemez: {OLLAMA_URL}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama kontrol hatası: {e}")
        print("\n🔧 Ollama kurulum talimatları:")
        print("1. Ollama'yı kurun: curl -fsSL https://ollama.ai/install.sh | sh")
        print("2. Servisi başlatın: ollama serve")
        print(f"3. Modeli indirin: ollama pull {OLLAMA_MODEL}")
        return False

def check_files():
    """Gerekli dosyaları kontrol et"""
    print("\n📁 Dosyalar kontrol ediliyor...")
    
    required_files = [
        'component_cards.py',
        'config.py', 
        'von.md',
        'requirements.txt'
    ]
    
    missing_files = []
    
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name}")
        else:
            missing_files.append(file_name)
            print(f"❌ {file_name}")
    
    if missing_files:
        print(f"\n❌ Eksik dosyalar: {', '.join(missing_files)}")
        return False
    else:
        print("✅ Tüm dosyalar mevcut")
        return True

def run_tests():
    """Testleri çalıştır"""
    print("\n🧪 Testler çalıştırılıyor...")
    
    try:
        if os.path.exists('test_data_processor.py'):
            result = subprocess.run([
                sys.executable, 'test_data_processor.py'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ Testler başarılı")
                return True
            else:
                print(f"❌ Test hatası: {result.stderr}")
                return False
        else:
            print("⚠️ Test dosyası bulunamadı, testler atlanıyor")
            return True
            
    except subprocess.TimeoutExpired:
        print("⏰ Testler zaman aşımına uğradı")
        return False
    except Exception as e:
        print(f"❌ Test çalıştırma hatası: {e}")
        return False

def run_streamlit():
    """Streamlit uygulamasını çalıştır"""
    print("\n🚀 Streamlit uygulaması başlatılıyor...")
    print("🌐 Tarayıcınızda http://localhost:8501 adresini açın")
    print("⏹️ Durdurmak için Ctrl+C tuşlayın")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "component_cards.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0"
        ])
    except KeyboardInterrupt:
        print("\n👋 Uygulama durduruldu")
    except Exception as e:
        print(f"❌ Streamlit hatası: {e}")

def main():
    """Ana fonksiyon"""
    print("🔧 LLM Veri Ön İşleme Uygulaması")
    print("=" * 50)
    
    # Kontroller
    if not check_files():
        print("\n❌ Gerekli dosyalar eksik, çıkılıyor...")
        return
    
    check_requirements()
    
    ollama_ok = check_ollama()
    if not ollama_ok:
        print("\n⚠️ Ollama problemi var, yine de devam ediliyor...")
    
    # Test seçeneği
    run_test = input("\n🧪 Testleri çalıştırmak istiyor musunuz? (y/N): ").lower().strip()
    if run_test in ['y', 'yes', 'evet']:
        test_ok = run_tests()
        if not test_ok:
            print("\n⚠️ Testler başarısız, yine de devam ediliyor...")
    
    # Streamlit başlat
    print("\n" + "=" * 50)
    run_streamlit()

if __name__ == "__main__":
    main()
