#!/usr/bin/env python3
"""
Issue 46 Test - Çoklu Bilgi Hücre Ayrıştırma
von.md'ye göre: Çoklu bilgi içeren hücrelerde ayrıştırma sorunları
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_46():
    """Issue 46'yı kapsamlı test et"""
    
    print("🧪 Issue 46 Test - Çoklu Bilgi Hücre Ayrıştırma")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # Kişi bilgileri
        "Ahmet Yılmaz, 30 yaş, İstanbul",
        "Mehmet <PERSON>, 25 yaş, Ankara, Mühendis",
        
        # Ürün bilgileri
        "Ürün A, 100 TL, Stokta",
        "Laptop, 5000 TL, 10 adet, Teknoloji",
        
        # <PERSON><PERSON>işim bilgileri
        "Email: <EMAIL>, Tel: 555-1234",
        "Adres: İstanbul, Telefon: 555-1234, Email: <EMAIL>",
        
        # İş bilgileri
        "Fiyat: 100 TL, Miktar: 5 adet, Tarih: 2024-01-01",
        "Müşteri: ABC Şirketi, Proje: Web Sitesi, Durum: Tamamlandı",
        
        # Şehir listeleri
        "İstanbul, Ankara, İzmir",
        "İstanbul, Ankara, İzmir, Bursa, Antalya",
        
        # Ürün listeleri
        "5 ürün, 2 yedek parça",
        "Bilgisayar, Mouse, Klavye, Monitör",
        
        # Tek bilgi (kontrol)
        "Ahmet Yılmaz",
        "100 TL"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 46 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 46]
            
            # Tek bilgi için tespit edilmemesi normal
            single_info = ["Ahmet Yılmaz", "100 TL"]
            is_single_info = test_case in single_info
            
            if is_single_info and not issue_detected:
                success_count += 1
                print(f"   ✅ BAŞARILI: Tek bilgi tespit edilmedi (normal)")
            elif issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 46 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 46 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_46()
