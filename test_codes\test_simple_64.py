#!/usr/bin/env python3
"""
Issue 64 Basit Test - Boş String Kontrolü
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_simple_64():
    """Issue 64'ü basit test et"""
    
    print("🧪 Issue 64 Basit Test - Boş String")
    print("=" * 40)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Boş string testi
    test_value = ""
    print(f"Test değeri: '{test_value}' (repr: {repr(test_value)})")
    
    # 1. Tespit kontrolü
    detected_issues = processor.detect_issues(test_value, "test_column")
    print(f"Tespit edilen issues: {detected_issues}")
    
    # 2. Process_value ile tam test
    processed_value, fixes = processor.process_value(test_value, "test_column")
    print(f"Processed değer: '{processed_value}'")
    print(f"Fixes: {fixes}")
    
    # Issue 64 kontrolü
    issue_64_fixes = [f for f in fixes if f['issue_id'] == 64]
    print(f"Issue 64 fixes: {issue_64_fixes}")
    
    if issue_64_fixes:
        print("✅ BAŞARILI: Boş string düzeltildi!")
        return True
    else:
        print("❌ BAŞARISIZ: Boş string düzeltilmedi!")
        return False

if __name__ == "__main__":
    test_simple_64()
