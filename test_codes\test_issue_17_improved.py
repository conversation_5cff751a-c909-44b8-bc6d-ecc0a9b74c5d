#!/usr/bin/env python3
"""
Issue 17 (<PERSON><PERSON><PERSON>n Kod Standardizasyonu) LLM İyileştirme Testi
Von.md'ye uygun test case'leri ile %100 başarı hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_17_improved():
    """Issue 17'yi LLM iyileştirmesi ile test et"""
    
    print("🧪 Issue 17 (Ürün Kod Standardizasyonu) LLM İyileştirme Testi")
    print("=" * 60)
    print("🎯 Hedef: %100 başarı - Von.md'ye uygun test case'leri")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Von.md'ye uygun test case'leri - Ürün kodları standart olmaması
    test_cases = [
        'P123',                  # Basit format
        'PRO123',               # Uzun prefix
        'Product_123',          # Underscore
        'PROD-123',             # Tire
        'P-123',                # <PERSON><PERSON>sa tire
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_17_detected = 17 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_17_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_17_fixes = [f for f in fixes if f['issue_id'] == 17]
            
            if issue_17_detected and issue_17_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_17_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_17_detected and not issue_17_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_17_detected}, Düzeltme={len(issue_17_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 17 Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 Issue 17 %100 BAŞARI!")
        return True
    else:
        print(f"   ⚠️ Issue 17 henüz %100 değil")
        return False

if __name__ == "__main__":
    success = test_issue_17_improved()
    
    if success:
        print("\n🎯 Issue 17 %100 başarıya ulaştı!")
        print("   ➡️ Issue 19'a geçilebilir")
    else:
        print("\n⚠️ Issue 17 daha fazla iyileştirme gerekiyor")
        print("   ➡️ LLM prompt'larını güçlendir")
