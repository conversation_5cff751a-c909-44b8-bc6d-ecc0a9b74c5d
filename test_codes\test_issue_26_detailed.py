#!/usr/bin/env python3
"""
Issue 26 (Fatura Detaylarında Farklı Yapılar) detaylı test scripti
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_26_detailed():
    """Issue 26'yı detaylıca ve itina ile test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri - Fatura yapısı standardizasyonu
    test_cases = [
        # <PERSON><PERSON> testler (standart olmayan fatura alanları)
        {
            'input': 'fatura no',
            'expected_detection': True,
            'expected_output': 'Fatura No',
            'description': 'Basit fatura no düzeltmesi'
        },
        {
            'input': 'fatura tarihi',
            'expected_detection': True,
            'expected_output': 'Fatura Tarihi',
            'description': 'Basit fatura tarihi düzeltmesi'
        },
        {
            'input': 'tutar',
            'expected_detection': True,
            'expected_output': 'Tutar',
            'description': 'Basit tutar düzeltmesi'
        },
        
        # Orta z<PERSON> testleri (İngilizce terimler)
        {
            'input': 'invoice number',
            'expected_detection': True,
            'expected_output': 'Fatura No',
            'description': 'İngilizce invoice number -> Fatura No'
        },
        {
            'input': 'invoice date',
            'expected_detection': True,
            'expected_output': 'Fatura Tarihi',
            'description': 'İngilizce invoice date -> Fatura Tarihi'
        },
        {
            'input': 'tax rate',
            'expected_detection': True,
            'expected_output': 'KDV Oranı',
            'description': 'İngilizce tax rate -> KDV Oranı'
        },
        
        # Zor testler (Karmaşık fatura yapıları)
        {
            'input': 'total amount',
            'expected_detection': True,
            'expected_output': 'Toplam Tutar',
            'description': 'İngilizce total amount -> Toplam Tutar'
        },
        {
            'input': 'customer name',
            'expected_detection': True,
            'expected_output': 'Müşteri Adı',
            'description': 'İngilizce customer name -> Müşteri Adı'
        },
        {
            'input': 'genel toplam',
            'expected_detection': True,
            'expected_output': 'Toplam Tutar',
            'description': 'Genel toplam -> Toplam Tutar'
        },
        
        # Negatif testler (zaten standart olanlar)
        {
            'input': 'Fatura No',
            'expected_detection': False,
            'expected_output': 'Fatura No',
            'description': 'Zaten standart fatura alanı'
        },
        {
            'input': 'Toplam Tutar',
            'expected_detection': False,
            'expected_output': 'Toplam Tutar',
            'description': 'Zaten standart fatura alanı'
        }
    ]
    
    print("🧪 Issue 26 (Fatura Detaylarında Farklı Yapılar) Detaylı Test")
    print("=" * 70)
    
    total_tests = len(test_cases)
    detection_success = 0
    fix_success = 0
    format_success = 0
    
    for i, test_case in enumerate(test_cases, 1):
        test_input = test_case['input']
        expected_detection = test_case['expected_detection']
        expected_output = test_case['expected_output']
        description = test_case['description']
        
        print(f"\n📝 Test {i}/{total_tests}: {description}")
        print(f"   📥 Girdi: '{test_input}'")
        print(f"   🎯 Beklenen tespit: {'✅' if expected_detection else '❌'}")
        print(f"   🎯 Beklenen çıktı: '{expected_output}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_input, "test_column")
        issue_26_detected = 26 in detected_issues
        
        print(f"   🔍 Gerçek tespit: {'✅' if issue_26_detected else '❌'} (Issues: {detected_issues})")
        
        # Tespit başarısı kontrolü
        detection_correct = (issue_26_detected == expected_detection)
        if detection_correct:
            detection_success += 1
            print(f"   ✅ Tespit başarılı")
        else:
            print(f"   ❌ Tespit başarısız - Beklenen: {expected_detection}, Gerçek: {issue_26_detected}")
        
        # 2. Düzeltme fonksiyonunu direkt test et (sadece tespit edilen durumlar için)
        if issue_26_detected and expected_detection:
            try:
                fixed_result = processor.fix_invoice_structure_issues(test_input)
                print(f"   🔧 Düzeltme sonucu: '{fixed_result}'")

                # Beklenen çıktı kontrolü
                if fixed_result == expected_output:
                    fix_success += 1
                    print(f"   ✅ Düzeltme başarılı")
                else:
                    print(f"   ⚠️ Düzeltme farklı - Beklenen: '{expected_output}', Gerçek: '{fixed_result}'")

                # Format kontrolü (Title Case)
                if fixed_result.istitle() or any(word.istitle() for word in fixed_result.split()):
                    format_success += 1
                    print(f"   ✅ Format doğru")
                else:
                    print(f"   ⚠️ Format hatası: '{fixed_result}'")

            except Exception as e:
                print(f"   ❌ Düzeltme hatası: {e}")
        elif not expected_detection:
            # Negatif testler için otomatik başarı
            fix_success += 1
            format_success += 1

        # 3. Process_value ile tam test
        try:
            processed_value, fixes = processor.process_value(test_input, "test_column")

            # Issue 26 düzeltmesi uygulandı mı?
            issue_26_fixes = [f for f in fixes if f['issue_id'] == 26]
            if issue_26_fixes:
                print(f"   ✅ Process_value düzeltmesi: '{processed_value}'")
                for fix in issue_26_fixes:
                    print(f"      - {fix['original']} → {fix['fixed']}")
            elif expected_detection:
                print(f"   ⚠️ Process_value düzeltmesi uygulanmadı (beklenen: uygulanmalı)")

        except Exception as e:
            print(f"   ❌ Process_value hatası: {e}")
    
    print(f"\n" + "=" * 70)
    print(f"📊 Issue 26 Detaylı Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   🔍 Tespit başarısı: {detection_success}/{total_tests} ({(detection_success/total_tests)*100:.1f}%)")
    print(f"   🔧 Düzeltme başarısı: {fix_success}/{total_tests} ({(fix_success/total_tests)*100:.1f}%)")
    print(f"   📄 Format başarısı: {format_success}/{total_tests} ({(format_success/total_tests)*100:.1f}%)")
    
    # Başarı kriterleri
    detection_threshold = 0.8  # %80 tespit başarısı
    fix_threshold = 0.8       # %80 düzeltme başarısı
    format_threshold = 0.9    # %90 format başarısı
    
    detection_pass = (detection_success / total_tests) >= detection_threshold
    fix_pass = (fix_success / total_tests) >= fix_threshold
    format_pass = (format_success / total_tests) >= format_threshold
    
    print(f"\n🏆 Başarı Değerlendirmesi:")
    print(f"   🔍 Tespit başarısı: {'✅' if detection_pass else '❌'} (>= {detection_threshold*100:.0f}%)")
    print(f"   🔧 Düzeltme başarısı: {'✅' if fix_pass else '❌'} (>= {fix_threshold*100:.0f}%)")
    print(f"   📄 Format başarısı: {'✅' if format_pass else '❌'} (>= {format_threshold*100:.0f}%)")
    
    overall_success = detection_pass and fix_pass and format_pass
    
    if overall_success:
        print(f"   🎉 Issue 26 implementasyonu başarılı!")
    else:
        print(f"   ⚠️ Issue 26 implementasyonu iyileştirme gerekiyor")
        
        # Başarısızlık nedenlerini analiz et
        if not detection_pass:
            print(f"      - Tespit başarısı düşük: {(detection_success/total_tests)*100:.1f}%")
        if not fix_pass:
            print(f"      - Düzeltme başarısı düşük: {(fix_success/total_tests)*100:.1f}%")
        if not format_pass:
            print(f"      - Format başarısı düşük: {(format_success/total_tests)*100:.1f}%")
    
    return overall_success

if __name__ == "__main__":
    success = test_issue_26_detailed()
    
    if success:
        print("\n🎯 Issue 26 detaylı testleri başarıyla geçti!")
        print("   ➡️ Tüm veri üzerinde detaylı test yapmaya hazır")
    else:
        print("\n⚠️ Issue 26 iyileştirme gerekiyor")
        print("   ➡️ Önce sorunları düzelt, sonra devam et")
