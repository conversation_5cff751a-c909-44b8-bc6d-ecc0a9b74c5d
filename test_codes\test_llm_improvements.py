#!/usr/bin/env python3
"""
LLM tabanlı iyileştirmeleri test et - Issue 21-22 %100 başarı için
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_21_llm():
    """Issue 21 LLM iyileştirmelerini test et"""
    
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Kritik başarısız test case'leri
    critical_cases = [
        '1yılgaranti',
        'iadeedilmez', 
        '60günvade'
    ]
    
    print("🧪 Issue 21 LLM İyileştirme Testi")
    print("=" * 50)
    
    success_count = 0
    
    for i, test_value in enumerate(critical_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_21_detected = 21 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_21_detected else '❌'}")
        
        if issue_21_detected:
            # Process_value ile tam test
            try:
                processed_value, fixes = processor.process_value(test_value, "test_column")
                issue_21_fixes = [f for f in fixes if f['issue_id'] == 21]
                
                if issue_21_fixes:
                    success_count += 1
                    print(f"   ✅ LLM Başarılı: '{processed_value}'")
                else:
                    print(f"   ❌ LLM Başarısız")
            except Exception as e:
                print(f"   ❌ Hata: {e}")
        else:
            print(f"   ❌ LLM Tespit Başarısız")
    
    success_rate = (success_count / len(critical_cases)) * 100
    print(f"\n📊 Issue 21 LLM Sonuçları: {success_rate:.1f}% ({success_count}/{len(critical_cases)})")
    
    return success_count == len(critical_cases)

def test_issue_22_llm():
    """Issue 22 LLM iyileştirmelerini test et"""
    
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Kritik başarısız test case'leri
    critical_cases = [
        '100000 TL',
        '50000 USD',
        '25000 EUR'
    ]
    
    print("\n🧪 Issue 22 LLM İyileştirme Testi")
    print("=" * 50)
    
    success_count = 0
    
    for i, test_value in enumerate(critical_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_22_detected = 22 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_22_detected else '❌'}")
        
        if issue_22_detected:
            # Process_value ile tam test
            try:
                processed_value, fixes = processor.process_value(test_value, "test_column")
                issue_22_fixes = [f for f in fixes if f['issue_id'] == 22]
                
                if issue_22_fixes:
                    success_count += 1
                    print(f"   ✅ LLM Başarılı: '{processed_value}'")
                else:
                    print(f"   ❌ LLM Başarısız")
            except Exception as e:
                print(f"   ❌ Hata: {e}")
        else:
            print(f"   ❌ LLM Tespit Başarısız")
    
    success_rate = (success_count / len(critical_cases)) * 100
    print(f"\n📊 Issue 22 LLM Sonuçları: {success_rate:.1f}% ({success_count}/{len(critical_cases)})")
    
    return success_count == len(critical_cases)

if __name__ == "__main__":
    print("🎯 LLM Tabanlı İyileştirme Testi")
    print("=" * 60)
    print("En büyük öncelik: LLM kullanarak çözüm üretmek!")
    
    # Issue 21 LLM testi
    issue_21_success = test_issue_21_llm()
    
    # Issue 22 LLM testi  
    issue_22_success = test_issue_22_llm()
    
    print(f"\n" + "=" * 60)
    print(f"🏆 LLM Tabanlı İyileştirme Sonuçları:")
    print(f"   Issue 21: {'✅ %100 LLM Başarısı' if issue_21_success else '❌ LLM Daha Fazla İyileştirme Gerekli'}")
    print(f"   Issue 22: {'✅ %100 LLM Başarısı' if issue_22_success else '❌ LLM Daha Fazla İyileştirme Gerekli'}")
    
    if issue_21_success and issue_22_success:
        print(f"   🎉 LLM tabanlı çözümler %100 başarıya ulaştı!")
    else:
        print(f"   ⚠️ LLM çözümleri daha fazla iyileştirme gerekiyor")
