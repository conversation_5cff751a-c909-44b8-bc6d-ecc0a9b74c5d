#!/usr/bin/env python3
"""
Tek Worker Test - Ollama Bottleneck Kontrolü
Aynı test case'leri tek worker ile çalıştırıp sonuçları karşılaştır
"""

import time
from datetime import datetime
from component_cards import DataQualityProcessor, OllamaLLMClient

def test_problematic_issues():
    """Paralel testte sorunlu olan issue'ları tek worker ile test et"""
    
    print("🧪 Tek Worker Ollama Bottleneck Testi")
    print("=" * 60)
    print(f"🕐 Başlama zamanı: {datetime.now()}")
    print("🎯 Amaç: Ollama bottleneck kontrolü")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Paralel testte sorunlu olan issue'lar
    problematic_issues = {
        3: {
            "description": "Metin verilerinde dil farklılıkları",
            "cases": ['Good quality', '<PERSON>yi kalite', 'Excellent service', '<PERSON>ü<PERSON><PERSON>l hizmet', 'Customer feedback', 'Müşteri geri bildirimi'],
            "parallel_result": "33.3%"
        },
        6: {
            "description": "Adres bilgisinde farklı standartlar", 
            "cases": ['Sokak No, Şehir, Ülke', 'Şehir, Sokak Adı, Ülke', 'İstanbul', 'Ankara, Türkiye', 'Atatürk Cad. No:123, İstanbul', 'Beyoğlu/İstanbul'],
            "parallel_result": "66.7%"
        },
        7: {
            "description": "Ürün kategorisinde farklı isimlendirme",
            "cases": ['Elektronik Kart', 'Electronic Card', 'Kart Elektronik', 'Elektronik Kartı', 'E-Kart', 'Digital Card'],
            "parallel_result": "66.7%"
        },
        21: {
            "description": "Sözleşme ve sipariş koşullarında metin temelli veriler",
            "cases": ["ödeme koşulları esnektir", "ödeme 30 gün içerisinde yapılır", "Net 30 gün", "Peşin ödeme", "esnek ödeme", "30 günde ödeme"],
            "parallel_result": "50.0%"
        }
    }
    
    start_time = time.time()
    
    for issue_id, issue_data in problematic_issues.items():
        print(f"\n🔍 Issue {issue_id}: {issue_data['description']}")
        print(f"   📊 Paralel sonuç: {issue_data['parallel_result']}")
        
        test_cases = issue_data['cases']
        detected_count = 0
        fixed_count = 0
        
        issue_start = time.time()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"   📝 Test {i}: '{test_case}'")
            
            try:
                # Tespit
                detected_issues = processor.detect_issues(test_case, "test_column")
                detected = issue_id in detected_issues
                
                print(f"      🔍 Tespit: {'✅' if detected else '❌'} (Issues: {detected_issues})")
                
                if detected:
                    detected_count += 1
                    
                    # Düzeltme
                    processed_value, fixes = processor.process_value(test_case, "test_column")
                    issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                    
                    if issue_fixes:
                        fixed_count += 1
                        print(f"      🔧 Düzeltme: ✅ '{processed_value}'")
                    else:
                        print(f"      🔧 Düzeltme: ❌")
                else:
                    print(f"      🔧 Düzeltme: - (tespit edilmedi)")
                    
            except Exception as e:
                print(f"      ❌ Hata: {e}")
        
        issue_time = time.time() - issue_start
        
        detection_rate = (detected_count / len(test_cases)) * 100
        fix_rate = (fixed_count / len(test_cases)) * 100
        
        print(f"   📊 Tek Worker Sonuç:")
        print(f"      Tespit: {detection_rate:.1f}% ({detected_count}/{len(test_cases)})")
        print(f"      Düzeltme: {fix_rate:.1f}% ({fixed_count}/{len(test_cases)})")
        print(f"      Süre: {issue_time:.1f}s")
        print(f"   🔄 Paralel vs Tek Worker:")
        
        parallel_rate = float(issue_data['parallel_result'].replace('%', ''))
        if detection_rate > parallel_rate:
            print(f"      ✅ Tek worker DAHA İYİ: {detection_rate:.1f}% > {parallel_rate}%")
            print(f"      🚨 OLLAMA BOTTLENECK TESPİT EDİLDİ!")
        elif detection_rate == parallel_rate:
            print(f"      ➡️ Aynı sonuç: {detection_rate:.1f}% = {parallel_rate}%")
        else:
            print(f"      ❌ Tek worker daha kötü: {detection_rate:.1f}% < {parallel_rate}%")
    
    total_time = time.time() - start_time
    
    print(f"\n📊 OLLAMA BOTTLENECK ANALİZİ")
    print("=" * 60)
    print(f"⏱️ Toplam tek worker süresi: {total_time:.2f} saniye")
    print(f"🔄 Paralel test süresi: 535 saniye")
    print(f"⚡ Hız karşılaştırması: Paralel {535/total_time:.1f}x daha yavaş")
    
    print(f"\n🎯 SONUÇ:")
    print(f"   Eğer tek worker sonuçları daha iyi ise:")
    print(f"   🚨 OLLAMA BOTTLENECK VAR!")
    print(f"   💡 Çözüm: Worker sayısını azalt veya LLM pool kullan")

if __name__ == "__main__":
    test_problematic_issues()
