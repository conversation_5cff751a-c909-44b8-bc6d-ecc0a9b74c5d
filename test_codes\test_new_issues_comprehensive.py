#!/usr/bin/env python3
"""
Yeni eklenen Issue 21-23'ün kapsamlı testte performansını kontrol et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_new_issues_in_comprehensive():
    """Yeni issue'ların kapsamlı testte performansını kontrol et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Kapsamlı testteki tüm test case'leri
    all_test_cases = [
        # Para birimi (Issue 1, 20)
        '1000 TL', '500 USD', '750 EUR', '1,500.75 TL', '2.500,50 USD', '€ 1000', '₺1500', '$500.25',
        
        # Tarih (Issue 2, 13, 44)
        '25-12-2023', '2023/12/25', '01.01.2024', '1 Ocak 2024', 'Jan 1, 2024', '2024-12-25T10:30:00',
        
        # <PERSON><PERSON>er test case'leri...
        'Hello Merhaba', 'Turkey', '+90 555 123 45 67', 'Atatürk Cad. No:123',
        'Elektronik Eşya', 'UTC', '5 kg', '12345678901', '1,000.50', 'Toptan Müşteri',
        '2024-01-01', '1000 TL + KDV', 'ABC Ltd.', 'UTC 10:00', 'P123', '5 kutu',
        '50 çalışan', '1000 USD (kur)'
    ]
    
    print("🔍 Yeni Issue'ların Kapsamlı Test Performansı")
    print("=" * 60)
    
    # Issue 21, 22, 23 için özel test case'leri ekle
    new_issue_cases = [
        # Issue 21: Sözleşme koşulları
        "Net 30 gün", "Peşin ödeme", "1 yıl garanti", "net30gün", "peşinödeme", "exworks",
        
        # Issue 22: Kredi limitleri
        "50k TL", "10k USD", "1M TL", "500 bin TL", "2 milyon USD",
        
        # Issue 23: Kampanya ve indirim
        "20% indirim", "100 TL indirim", "%20 off", "erkenödeme", "toplualım kampanyası"
    ]
    
    # Tüm test case'leri birleştir
    all_cases = all_test_cases + new_issue_cases
    
    total_tests = len(all_cases)
    issue_21_detections = 0
    issue_22_detections = 0
    issue_23_detections = 0
    
    issue_21_fixes = 0
    issue_22_fixes = 0
    issue_23_fixes = 0
    
    print(f"📝 Toplam test case sayısı: {total_tests}")
    print(f"🎯 Yeni issue'lar: 21, 22, 23")
    
    for i, test_case in enumerate(all_cases, 1):
        if i % 10 == 0:  # Her 10 testte bir progress göster
            print(f"   📊 İlerleme: {i}/{total_tests}")
        
        try:
            # Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            
            # Issue 21, 22, 23 tespit edildi mi?
            if 21 in detected_issues:
                issue_21_detections += 1
            if 22 in detected_issues:
                issue_22_detections += 1
            if 23 in detected_issues:
                issue_23_detections += 1
            
            # Düzeltme kontrolü
            processed_value, fixes = processor.process_value(test_case, "test_column")
            
            # Issue 21, 22, 23 düzeltmesi uygulandı mı?
            applied_issues = [f['issue_id'] for f in fixes]
            if 21 in applied_issues:
                issue_21_fixes += 1
            if 22 in applied_issues:
                issue_22_fixes += 1
            if 23 in applied_issues:
                issue_23_fixes += 1
                
        except Exception as e:
            print(f"   ❌ Test hatası ({test_case}): {e}")
    
    print(f"\n📊 Yeni Issue'lar Performans Raporu:")
    print("-" * 50)
    
    print(f"Issue 21 (Sözleşme Koşulları):")
    print(f"   🔍 Tespit: {issue_21_detections}")
    print(f"   🔧 Düzeltme: {issue_21_fixes}")
    print(f"   📈 Düzeltme oranı: {(issue_21_fixes/issue_21_detections)*100:.1f}%" if issue_21_detections > 0 else "   📈 Düzeltme oranı: 0%")
    
    print(f"\nIssue 22 (Kredi Limitleri):")
    print(f"   🔍 Tespit: {issue_22_detections}")
    print(f"   🔧 Düzeltme: {issue_22_fixes}")
    print(f"   📈 Düzeltme oranı: {(issue_22_fixes/issue_22_detections)*100:.1f}%" if issue_22_detections > 0 else "   📈 Düzeltme oranı: 0%")
    
    print(f"\nIssue 23 (Kampanya İndirim):")
    print(f"   🔍 Tespit: {issue_23_detections}")
    print(f"   🔧 Düzeltme: {issue_23_fixes}")
    print(f"   📈 Düzeltme oranı: {(issue_23_fixes/issue_23_detections)*100:.1f}%" if issue_23_detections > 0 else "   📈 Düzeltme oranı: 0%")
    
    # Genel değerlendirme
    total_new_detections = issue_21_detections + issue_22_detections + issue_23_detections
    total_new_fixes = issue_21_fixes + issue_22_fixes + issue_23_fixes
    
    print(f"\n🏆 Genel Yeni Issue Performansı:")
    print(f"   🔍 Toplam tespit: {total_new_detections}")
    print(f"   🔧 Toplam düzeltme: {total_new_fixes}")
    print(f"   📈 Genel düzeltme oranı: {(total_new_fixes/total_new_detections)*100:.1f}%" if total_new_detections > 0 else "   📈 Genel düzeltme oranı: 0%")
    
    # Başarı kriterleri
    success_criteria = {
        'detection_rate': total_new_detections >= 10,  # En az 10 tespit
        'fix_rate': total_new_fixes >= total_new_detections * 0.8,  # %80 düzeltme
    }
    
    print(f"\n✅ Başarı Kriterleri:")
    print(f"   🔍 Tespit yeterli: {'✅' if success_criteria['detection_rate'] else '❌'} (>= 10)")
    print(f"   🔧 Düzeltme yeterli: {'✅' if success_criteria['fix_rate'] else '❌'} (>= 80%)")
    
    overall_success = all(success_criteria.values())
    print(f"\n🎯 Genel Başarı: {'✅ Başarılı' if overall_success else '❌ İyileştirme gerekli'}")
    
    return overall_success

if __name__ == "__main__":
    print("🧪 Yeni Issue'lar Kapsamlı Test Kontrolü")
    print("=" * 60)
    
    success = test_new_issues_in_comprehensive()
    
    if success:
        print("\n🎉 Yeni issue'lar kapsamlı testte başarılı performans gösteriyor!")
    else:
        print("\n⚠️ Yeni issue'lar iyileştirme gerekiyor.")
