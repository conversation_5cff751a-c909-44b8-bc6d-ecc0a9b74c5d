#!/usr/bin/env python3
"""
Issue 3 (Dil Standardizasyonu) detaylı test scripti - %100 başarı için
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_3_detailed():
    """Issue 3'ü %100 başarı için test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Kapsamlı testteki başarısız test case'leri
    failing_cases = [
        'Hello Merhaba',
        'Customer müşteri',
        'Product ürün',
        'Customer müşteri service hizmet',
        'Product ürün quality kalite',
        'Good İyi service hizmet',
        'Technology teknoloji computer bilgisayar'
    ]
    
    print("🧪 Issue 3 (Dil Standardizasyonu) %100 Başarı Testi")
    print("=" * 60)
    
    success_count = 0
    
    for i, test_value in enumerate(failing_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_3_detected = 3 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_3_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_3_detected:
            # 2. Düzeltme fonksiyonunu direkt test et
            try:
                fixed_result = processor.fix_language_issues(test_value)
                print(f"   🔧 Düzeltme sonucu: '{fixed_result}'")
                
                # Türkçe mi?
                if fixed_result != test_value:
                    print(f"   ✅ Düzeltme uygulandı")
                else:
                    print(f"   ⚠️ Düzeltme uygulanmadı")
                    
            except Exception as e:
                print(f"   ❌ Düzeltme hatası: {e}")
        
        # 3. Process_value ile tam test
        try:
            processed_value, fixes = processor.process_value(test_value, "test_column")
            
            # Issue 3 düzeltmesi uygulandı mı?
            issue_3_fixes = [f for f in fixes if f['issue_id'] == 3]
            if issue_3_fixes:
                success_count += 1
                print(f"   ✅ Agresif strateji başarılı: '{processed_value}'")
                for fix in issue_3_fixes:
                    print(f"      - {fix['original']} → {fix['fixed']}")
            else:
                print(f"   ❌ Agresif strateji başarısız - düzeltme uygulanmadı")
                
        except Exception as e:
            print(f"   ❌ Process_value hatası: {e}")
    
    print(f"\n📊 Issue 3 Test Sonuçları:")
    print(f"   📝 Test sayısı: {len(failing_cases)}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {(success_count/len(failing_cases))*100:.1f}%")
    
    if success_count == len(failing_cases):
        print(f"   🎉 Issue 3 %100 başarı elde edildi!")
        return True
    else:
        print(f"   ⚠️ Issue 3 hala %100 değil - daha fazla iyileştirme gerekli")
        return False

if __name__ == "__main__":
    success = test_issue_3_detailed()
    
    if success:
        print("\n🎯 Issue 3 %100 başarıya ulaştı!")
        print("   ➡️ Kapsamlı test yapmaya hazır")
    else:
        print("\n⚠️ Issue 3 daha fazla iyileştirme gerekiyor")
        print("   ➡️ Önce %100'e çıkar, sonra devam et")
