#!/usr/bin/env python3
"""
19042024.py'deki DSİ ve Multinet özel durumlarını analiz et
"""

import pandas as pd
import re
from collections import Counter

def analyze_dsi_case():
    """DSİ firma normalizasyonu analizi"""
    print("🏢 DSİ FİRMA NORMALİZASYONU ANALİZİ")
    print("=" * 60)
    
    # 19042024.py'deki DSİ normalizasyon kuralları
    dsi_rules = {
        "DSİ 12 BLG.": "DSİ 12. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ 12. BLG": "DSİ 12. BÖLGE MÜDÜRLÜĞÜ", 
        "DSİ 12. BÖLGE MÜDÜRLÜĞÜ KAYSERİ": "DSİ 12. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ 13 BLG.": "DSİ 13. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ 13. BLG": "DSİ 13. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ. 13 BLG": "DSİ 13. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ 13 BÖLGE MÜDÜRLÜĞÜ": "DSİ 13. <PERSON>ÖL<PERSON> MÜDÜRLÜĞÜ",
        "DSİ 13 BÖLGE MÜDÜRLÜĞÜ - ANTALYA": "DSİ 13. BÖL<PERSON> MÜDÜRLÜĞÜ",
        "DSİ 13. BÖLGE MÜDÜRLÜĞÜ ANTALYA": "DSİ 13. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ 18 BLG.": "DSİ 18. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ 18. BLG": "DSİ 18. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ 19. BLG": "DSİ 19. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ 19. BÖLGE MÜDÜRLÜĞÜ SİVAS": "DSİ 19. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ. 12 BLG": "DSİ 12. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ. 19 BLG": "DSİ 19. BÖLGE MÜDÜRLÜĞÜ",
        "DSİ. 4 BLG": "DSİ 4. BÖLGE MÜDÜRLÜĞÜ"
    }
    
    print(f"📋 Toplam DSİ normalizasyon kuralı: {len(dsi_rules)}")
    print(f"\n🔍 PATTERN ANALİZİ:")
    
    # Pattern'leri analiz et
    patterns = {
        "Nokta eksikliği": [],
        "Şehir adı eklenmesi": [],
        "Kısaltma farklılıkları": [],
        "Boşluk/nokta karışıklığı": []
    }
    
    for original, normalized in dsi_rules.items():
        if "KAYSERİ" in original or "ANTALYA" in original or "SİVAS" in original:
            patterns["Şehir adı eklenmesi"].append(f"'{original}' → '{normalized}'")
        elif "BLG." in original and "BÖLGE MÜDÜRLÜĞÜ" in normalized:
            patterns["Kısaltma farklılıkları"].append(f"'{original}' → '{normalized}'")
        elif "DSİ." in original:
            patterns["Boşluk/nokta karışıklığı"].append(f"'{original}' → '{normalized}'")
        else:
            patterns["Nokta eksikliği"].append(f"'{original}' → '{normalized}'")
    
    for pattern_type, examples in patterns.items():
        if examples:
            print(f"\n   📌 {pattern_type}:")
            for example in examples[:3]:  # İlk 3 örnek
                print(f"      {example}")
            if len(examples) > 3:
                print(f"      ... ve {len(examples)-3} örnek daha")
    
    print(f"\n🎯 DSİ NORMALIZASYONU PROBLEM TİPLERİ:")
    print(f"   1. Kısaltma standardizasyonu: BLG. → BÖLGE MÜDÜRLÜĞÜ")
    print(f"   2. Noktalama düzeltmesi: DSİ. → DSİ")
    print(f"   3. Şehir adı temizleme: KAYSERİ, ANTALYA, SİVAS kaldırma")
    print(f"   4. Boşluk normalizasyonu: Tutarsız boşluklar")
    
    # Von.md'deki hangi issue'lara karşılık geldiği
    print(f"\n📋 VON.MD İSSUE KARŞILIĞI:")
    print(f"   Issue 15: Müşteri Firmalara Ait İsimlendirme Farklılıkları ✅")
    print(f"   Issue 34: Tekrar Eden Müşteri Bilgileri ✅") 
    print(f"   Issue 42: El Yazımı Hataları ve Yazım Yanlışları ✅")
    print(f"   Issue 49: Boşluk Sorunları (Trimlenmemiş Veriler) ✅")
    
    return dsi_rules

def analyze_multinet_case():
    """Multinet hardcoded veri doldurma analizi"""
    print("\n💳 MULTİNET HARDCODED VERİ DOLDURMA ANALİZİ")
    print("=" * 60)
    
    # 19042024.py'deki Multinet kuralları
    multinet_rules = [
        {
            "condition": "Firma == 'Multinet Kurumsal Hiz.A.Ş.'",
            "action": "VergiNo = 6230090752",
            "reason": "Boş vergi numarası doldurma"
        },
        {
            "condition": "Firma == 'ESNEK YAN HAKLAR DANIŞMANLIK HİZMETLERİ LTD. ŞTİ.'",
            "action": "VergiNo = 3800523520", 
            "reason": "Boş vergi numarası doldurma"
        }
    ]
    
    print(f"📋 Hardcoded veri doldurma kuralı: {len(multinet_rules)}")
    
    for i, rule in enumerate(multinet_rules, 1):
        print(f"\n   {i}. KURAL:")
        print(f"      Koşul: {rule['condition']}")
        print(f"      Eylem: {rule['action']}")
        print(f"      Sebep: {rule['reason']}")
    
    print(f"\n🚨 HARDCODED YAKLAŞIMIN PROBLEMLERİ:")
    print(f"   1. ❌ Ölçeklenebilir değil - Her firma için ayrı kod")
    print(f"   2. ❌ Bakım zorluğu - Yeni firmalar için kod değişikliği")
    print(f"   3. ❌ Hata riski - Manuel vergi numarası girişi")
    print(f"   4. ❌ Generic olmayan - Sadece bu 2 firma için çalışır")
    print(f"   5. ❌ Test edilemez - Vergi numarası doğruluğu kontrol edilmiyor")
    
    print(f"\n📋 VON.MD İSSUE KARŞILIĞI:")
    print(f"   Issue 55: Vergi Numarasının Eksik veya Hatalı Girilmesi ✅")
    print(f"   Issue 64: Eksik Verilerin Farklı Şekillerde Gösterimi ✅")
    
    return multinet_rules

def propose_generic_solutions():
    """Generic çözüm önerileri"""
    print("\n🔧 GENERİK ÇÖZÜM ÖNERİLERİ")
    print("=" * 60)
    
    print("🏢 DSİ NORMALIZASYONU İÇİN GENERİK ÇÖZÜM:")
    print("""
    def normalize_company_name(company_name):
        # LLM tabanlı normalizasyon
        prompt = f'''
        Şirket adını standart formata çevir:
        Girdi: "{company_name}"
        
        Kurallar:
        1. Kısaltmaları açık hale getir (BLG. → BÖLGE MÜDÜRLÜĞÜ)
        2. Şehir adlarını kaldır
        3. Noktalama düzelt
        4. Boşlukları normalize et
        
        Çıktı: Standart şirket adı
        '''
        return llm_client.generate(prompt)
    """)
    
    print("💳 MULTİNET VERİ DOLDURMA İÇİN GENERİK ÇÖZÜM:")
    print("""
    def fill_missing_tax_number(company_name):
        # Vergi numarası veritabanı sorgusu
        tax_db = load_tax_number_database()
        
        # Fuzzy matching ile firma bul
        matches = fuzzy_search(company_name, tax_db)
        
        if matches:
            return matches[0]['tax_number']
        
        # LLM ile vergi numarası format kontrolü
        if is_valid_tax_number_format(suggested_number):
            return suggested_number
            
        return None
    """)
    
    print("🤖 COMPONENT_CARDS.PY'DEKİ ÇÖZÜM:")
    print("""
    # Issue 15: Firma adı normalizasyonu
    def fix_company_name_variations(value):
        prompt = f"Şirket adını standart formata çevir: {value}"
        return llm_client.generate(prompt)
    
    # Issue 64: Eksik veri doldurma  
    def fix_missing_data(value, column_name):
        if value in ['', 'NULL', 'N/A']:
            return "Veri Yok"  # Standart eksik veri gösterimi
        return value
    """)

def analyze_test_xlsx_with_rules():
    """test.xlsx'i DSİ ve Multinet kuralları ile analiz et"""
    print("\n📊 TEST.XLSX'DE DSİ VE MULTİNET ANALİZİ")
    print("=" * 60)
    
    try:
        df = pd.read_excel('test.xlsx')
        
        # İlk satır başlık olarak ayarla
        df.columns = df.iloc[1]  # 2. satır başlık
        df = df.drop([0, 1]).reset_index(drop=True)  # İlk 2 satırı sil
        
        print(f"✅ Dosya okundu: {df.shape[0]} satır, {df.shape[1]} kolon")
        print(f"📋 Kolonlar: {list(df.columns)}")
        
        # DSİ firmalarını bul
        if 'Firma' in df.columns:
            dsi_firms = df[df['Firma'].str.contains('DSİ', na=False, case=False)]
            print(f"\n🏢 DSİ FİRMALARI:")
            print(f"   Toplam DSİ kaydı: {len(dsi_firms)}")
            
            if len(dsi_firms) > 0:
                unique_dsi = dsi_firms['Firma'].value_counts()
                print(f"   Unique DSİ firma adı: {len(unique_dsi)}")
                print(f"   En çok tekrar eden DSİ adları:")
                for name, count in unique_dsi.head(5).items():
                    print(f"      '{name}': {count} kez")
                
                # DSİ normalizasyon ihtiyacı analizi
                dsi_variations = []
                for firm_name in unique_dsi.index:
                    if any(pattern in firm_name for pattern in ['BLG.', 'BLG', 'BÖLGE']):
                        dsi_variations.append(firm_name)
                
                print(f"\n   🔧 Normalizasyon gereken DSİ adları: {len(dsi_variations)}")
                for variation in dsi_variations[:5]:
                    print(f"      '{variation}'")
            
            # Multinet firmalarını bul
            multinet_firms = df[df['Firma'].str.contains('Multinet', na=False, case=False)]
            print(f"\n💳 MULTİNET FİRMALARI:")
            print(f"   Toplam Multinet kaydı: {len(multinet_firms)}")
            
            if len(multinet_firms) > 0:
                unique_multinet = multinet_firms['Firma'].value_counts()
                print(f"   Unique Multinet firma adı: {len(unique_multinet)}")
                for name, count in unique_multinet.items():
                    print(f"      '{name}': {count} kez")
        
        # Eksik vergi numarası analizi (eğer varsa)
        if 'VergiNo' in df.columns:
            missing_tax = df[df['VergiNo'].isnull() | (df['VergiNo'] == '')]
            print(f"\n📊 EKSİK VERGİ NUMARASI:")
            print(f"   Eksik vergi numarası: {len(missing_tax)} kayıt")
            
            if len(missing_tax) > 0:
                print(f"   Eksik vergi numaralı firmalar:")
                for firm in missing_tax['Firma'].head(5):
                    print(f"      '{firm}'")
        
    except Exception as e:
        print(f"❌ Dosya analiz hatası: {e}")

if __name__ == "__main__":
    # DSİ analizi
    dsi_rules = analyze_dsi_case()
    
    # Multinet analizi  
    multinet_rules = analyze_multinet_case()
    
    # Generic çözüm önerileri
    propose_generic_solutions()
    
    # test.xlsx analizi
    analyze_test_xlsx_with_rules()
    
    print(f"\n🎯 SONUÇ:")
    print(f"✅ DSİ normalizasyonu: Von.md Issue 15, 34, 42, 49'u çözüyor")
    print(f"✅ Multinet veri doldurma: Von.md Issue 55, 64'ü çözüyor") 
    print(f"❌ Ancak hardcoded yaklaşım ölçeklenebilir değil")
    print(f"💡 Component_cards.py generic LLM tabanlı çözüm sunuyor")
