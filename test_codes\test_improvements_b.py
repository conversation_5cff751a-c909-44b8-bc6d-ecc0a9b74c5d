#!/usr/bin/env python3
"""
B) Adımı iyileştirmelerini test et - Issue 2 ve 16 agresif strateji
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_2_improvements():
    """Issue 2 (Tarih format) iyileştirmelerini test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Önceden başarısız olan test case'leri
    challenging_cases = [
        "1 Ocak 2024",
        "Jan 1, 2024", 
        "Pazartesi, 1 Ocak 2024",
        "Monday, Jan 1, 2024",
        "2024-12-25T10:30:00"
    ]
    
    print("🔍 Issue 2 (Tarih Format) İyileştirme Testi")
    print("=" * 50)
    
    success_count = 0
    
    for i, test_value in enumerate(challenging_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_2_detected = 2 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_2_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_2_detected:
            # 2. Process_value ile tam test
            try:
                processed_value, fixes = processor.process_value(test_value, "test_column")
                
                # Issue 2 düzeltmesi uygulandı mı?
                issue_2_fixes = [f for f in fixes if f['issue_id'] == 2]
                if issue_2_fixes:
                    success_count += 1
                    print(f"   ✅ Agresif strateji başarılı: '{processed_value}'")
                    for fix in issue_2_fixes:
                        print(f"      - {fix['original']} → {fix['fixed']}")
                        
                    # Sonuç YYYY-MM-DD formatında mı?
                    import re
                    if re.match(r'^\d{4}-\d{2}-\d{2}$', fix['fixed']):
                        print(f"      ✅ Doğru format: YYYY-MM-DD")
                    else:
                        print(f"      ⚠️ Format kontrolü: {fix['fixed']}")
                else:
                    print(f"   ❌ Agresif strateji başarısız - düzeltme uygulanmadı")
                    
            except Exception as e:
                print(f"   ❌ Process_value hatası: {e}")
        else:
            print(f"   ❌ Tespit başarısız")
    
    print(f"\n📊 Issue 2 İyileştirme Sonuçları:")
    print(f"   📝 Test sayısı: {len(challenging_cases)}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {(success_count/len(challenging_cases))*100:.1f}%")
    
    return success_count == len(challenging_cases)

def test_issue_16_improvements():
    """Issue 16 (Sözleşme zaman dilimi) iyileştirmelerini test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Önceden başarısız olan test case'leri
    challenging_cases = [
        "2024-01-01 10:00:00 UTC",
        "2024-01-01 13:00:00 +03:00",
        "10:30:00 GMT+3",
        "14:00 TRT"
    ]
    
    print("\n🔍 Issue 16 (Sözleşme Zaman Dilimi) İyileştirme Testi")
    print("=" * 50)
    
    success_count = 0
    
    for i, test_value in enumerate(challenging_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_16_detected = 16 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_16_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_16_detected:
            # 2. Process_value ile tam test
            try:
                processed_value, fixes = processor.process_value(test_value, "test_column")
                
                # Issue 16 düzeltmesi uygulandı mı?
                issue_16_fixes = [f for f in fixes if f['issue_id'] == 16]
                if issue_16_fixes:
                    success_count += 1
                    print(f"   ✅ Agresif strateji başarılı: '{processed_value}'")
                    for fix in issue_16_fixes:
                        print(f"      - {fix['original']} → {fix['fixed']}")
                        
                    # Sonuç UTC formatında mı?
                    if 'UTC' in fix['fixed']:
                        print(f"      ✅ Doğru format: UTC içeriyor")
                    else:
                        print(f"      ⚠️ Format kontrolü: {fix['fixed']}")
                else:
                    print(f"   ❌ Agresif strateji başarısız - düzeltme uygulanmadı")
                    
            except Exception as e:
                print(f"   ❌ Process_value hatası: {e}")
        else:
            print(f"   ❌ Tespit başarısız")
    
    print(f"\n📊 Issue 16 İyileştirme Sonuçları:")
    print(f"   📝 Test sayısı: {len(challenging_cases)}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {(success_count/len(challenging_cases))*100:.1f}%")
    
    return success_count == len(challenging_cases)

def test_agressive_strategy_effectiveness():
    """Agresif strateji etkinliğini test et"""
    
    print("\n🔥 Agresif Strateji Etkinlik Testi")
    print("=" * 50)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Issue çakışması olan test case'leri
    conflict_cases = [
        ("25-12-2023", [2, 13]),  # Tarih formatı + Sipariş tarih
        ("UTC 10:00", [8, 16]),   # Zaman dilimi + Sözleşme zaman dilimi
    ]
    
    for test_value, expected_issues in conflict_cases:
        print(f"\n📝 Çakışma Testi: '{test_value}'")
        print(f"   🎯 Beklenen issues: {expected_issues}")
        
        # Process_value ile test
        try:
            processed_value, fixes = processor.process_value(test_value, "test_column")
            
            # Her issue için düzeltme uygulandı mı?
            applied_issues = [f['issue_id'] for f in fixes]
            
            print(f"   🔧 Uygulanan düzeltmeler: {applied_issues}")
            print(f"   📄 Son değer: '{processed_value}'")
            
            # Beklenen issue'ların hepsi uygulandı mı?
            all_applied = all(issue in applied_issues for issue in expected_issues)
            
            if all_applied:
                print(f"   ✅ Agresif strateji başarılı - tüm issue'lar uygulandı")
            else:
                missing = [issue for issue in expected_issues if issue not in applied_issues]
                print(f"   ⚠️ Eksik issue'lar: {missing}")
                
        except Exception as e:
            print(f"   ❌ Test hatası: {e}")

if __name__ == "__main__":
    print("🧪 B) Adımı İyileştirmeleri Test Süreci")
    print("=" * 60)
    
    # Issue 2 iyileştirmelerini test et
    issue_2_success = test_issue_2_improvements()
    
    # Issue 16 iyileştirmelerini test et
    issue_16_success = test_issue_16_improvements()
    
    # Agresif strateji etkinliğini test et
    test_agressive_strategy_effectiveness()
    
    print(f"\n" + "=" * 60)
    print(f"🏆 B) Adımı İyileştirme Sonuçları:")
    print(f"   Issue 2 iyileştirme: {'✅ Başarılı' if issue_2_success else '❌ Başarısız'}")
    print(f"   Issue 16 iyileştirme: {'✅ Başarılı' if issue_16_success else '❌ Başarısız'}")
    
    if issue_2_success and issue_16_success:
        print(f"   🎉 B) Adımı iyileştirmeleri tamamen başarılı!")
    else:
        print(f"   ⚠️ B) Adımı iyileştirmeleri kısmen başarılı")
