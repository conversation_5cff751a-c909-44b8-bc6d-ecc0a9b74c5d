#!/usr/bin/env python3
"""
Excel test sonuçlarını analiz et
"""

import pandas as pd
import json
from datetime import datetime

def analyze_excel_results():
    """Excel test sonuçlarını analiz et"""
    
    print("🔍 Excel Test Sonuçları Analizi")
    print("=" * 60)
    
    # 1. Orijinal dosyayı oku
    try:
        original_df = pd.read_excel('test_data_quality.xlsx')
        print(f"📊 Orijinal dosya: {len(original_df)} satır, {len(original_df.columns)} kolon")
    except Exception as e:
        print(f"❌ Orijinal dosya okunamadı: {e}")
        return
    
    # 2. İşlenmiş dosyayı oku
    try:
        fixed_df = pd.read_excel('test_data_quality_fixed.xlsx')
        print(f"✅ İşlenmiş dosya: {len(fixed_df)} satır, {len(fixed_df.columns)} kolon")
    except Exception as e:
        print(f"❌ İşlenmiş dosya okunamadı: {e}")
        return
    
    # 3. İşleme logunu oku
    try:
        log_df = pd.read_excel('test_data_quality_processing_log.xlsx')
        print(f"📝 İşleme logu: {len(log_df)} satır")
    except Exception as e:
        print(f"❌ İşleme logu okunamadı: {e}")
        return
    
    print("\n" + "=" * 60)
    print("📈 İşleme İstatistikleri")
    print("=" * 60)
    
    # 4. İşleme istatistikleri
    total_cells = len(original_df) * len(original_df.columns)
    processed_cells = len(log_df)
    
    print(f"📊 Toplam hücre sayısı: {total_cells:,}")
    print(f"🔧 İşlenen hücre sayısı: {processed_cells:,}")
    print(f"📈 İşleme oranı: {(processed_cells/total_cells)*100:.1f}%")
    
    # 5. Issue türleri analizi
    if 'issue_id' in log_df.columns:
        issue_counts = log_df['issue_id'].value_counts().sort_index()
        print(f"\n🔍 Tespit Edilen Problem Türleri:")
        for issue_id, count in issue_counts.items():
            print(f"   Issue {issue_id}: {count:,} adet")
    
    # 6. Kolon bazlı analiz
    if 'column' in log_df.columns:
        column_counts = log_df['column'].value_counts()
        print(f"\n📋 Kolon Bazlı İşleme:")
        for column, count in column_counts.items():
            print(f"   {column}: {count:,} düzeltme")
    
    # 7. Örnek düzeltmeler
    print(f"\n🔧 Örnek Düzeltmeler (İlk 10):")
    print("-" * 60)
    
    if 'original' in log_df.columns and 'fixed' in log_df.columns:
        for i, row in log_df.head(10).iterrows():
            original = str(row.get('original', ''))[:30]
            fixed = str(row.get('fixed', ''))[:30]
            column = row.get('column', 'N/A')
            issue_id = row.get('issue_id', 'N/A')
            
            print(f"   {column} (Issue {issue_id}):")
            print(f"     Önce: '{original}{'...' if len(str(row.get('original', ''))) > 30 else ''}'")
            print(f"     Sonra: '{fixed}{'...' if len(str(row.get('fixed', ''))) > 30 else ''}'")
            print()
    
    # 8. Başarı oranı analizi
    print("📊 Başarı Oranı Analizi:")
    print("-" * 60)
    
    # Değişen hücre sayısı
    changed_cells = 0
    if 'original' in log_df.columns and 'fixed' in log_df.columns:
        changed_cells = len(log_df[log_df['original'] != log_df['fixed']])
    
    print(f"🔄 Değişen hücre sayısı: {changed_cells:,}")
    print(f"📈 Düzeltme başarı oranı: {(changed_cells/processed_cells)*100:.1f}%")
    
    # 9. Performans analizi
    if 'timestamp' in log_df.columns:
        print(f"\n⏱️ Performans Analizi:")
        print("-" * 60)
        
        # İlk ve son timestamp
        timestamps = pd.to_datetime(log_df['timestamp'])
        start_time = timestamps.min()
        end_time = timestamps.max()
        duration = (end_time - start_time).total_seconds()
        
        print(f"⏰ Başlangıç: {start_time}")
        print(f"⏰ Bitiş: {end_time}")
        print(f"⏱️ Toplam süre: {duration:.1f} saniye ({duration/60:.1f} dakika)")
        print(f"🚀 Hücre/saniye: {processed_cells/duration:.1f}")
    
    # 10. Veri kalitesi karşılaştırması
    print(f"\n📊 Veri Kalitesi Karşılaştırması:")
    print("-" * 60)
    
    # Örnek kolonlar için karşılaştırma
    sample_columns = ['Fiyat', 'Tarih', 'Telefon', 'TC_No']
    
    for col in sample_columns:
        if col in original_df.columns and col in fixed_df.columns:
            print(f"\n📋 {col} Kolonu:")
            print(f"   Orijinal örnekler:")
            for val in original_df[col].head(3):
                print(f"     - {val}")
            print(f"   Düzeltilmiş örnekler:")
            for val in fixed_df[col].head(3):
                print(f"     - {val}")

if __name__ == "__main__":
    analyze_excel_results()
