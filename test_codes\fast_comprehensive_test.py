#!/usr/bin/env python3
"""
Hızlı Comprehensive Test - Basit ve Güvenli
Paralel yerine optimizasyon odaklı yaklaşım
"""

import time
from datetime import datetime
from collections import defaultdict
from component_cards import DataQualityProcessor, OllamaLLMClient

class FastComprehensiveTest:
    """Hızlı kapsamlı test sistemi"""
    
    def __init__(self):
        # Tek LLM client kullan (conflict önlemi)
        self.llm_client = OllamaLLMClient()
        self.processor = DataQualityProcessor(self.llm_client)
        self.test_results = {}
        self.start_time = None
        
    def create_test_cases(self) -> dict:
        """Test case'leri oluştur - Sadece kritik Issue 21-26 odaklı"""
        return {
            21: {
                "description": "Sözleşme ve sipariş koşulları",
                "easy": ["Net 30 gün", "Peşin ödeme", "1 yıl garanti"],
                "hard": ["net30gün", "peşinödeme", "1yılgaranti", "iadeedilmez", "exworks", "60günvade"]
            },
            22: {
                "description": "Kredi limitleri farklı birimlendirmeler", 
                "easy": ["100000 TL", "50000 USD", "25000 EUR"],
                "hard": ["50k TL", "10k USD", "1M TL", "500 bin TL", "2 milyon USD", "100,000 TL", "50.5k EUR"]
            },
            23: {
                "description": "Kampanya ve indirim türleri",
                "easy": ["20% indirim", "100 TL indirim", "Erken ödeme"],
                "hard": ["%20 off", "100 lira indirim", "erkenödeme", "toplualım kampanyası", "sezon sonu %30", "yeni müşteri 50TL"]
            },
            24: {
                "description": "Ürün kategorilerinin standart olmaması",
                "easy": ["elektronik eşya", "ev aletleri", "beyaz eşya"],
                "hard": ["küçük ev aletleri", "büyük beyaz eşya", "giyim ve tekstil", "otomobil yedek parça", "spor malzemeleri ve fitness"]
            },
            25: {
                "description": "Ödeme türlerinde farklılık",
                "easy": ["kredi kart", "banka kartı", "nakit"],
                "hard": ["elektronik transfer", "banka havalesi", "taksitli ödeme", "cash on delivery", "credit card", "wire transfer"]
            },
            26: {
                "description": "Fatura detaylarında farklı yapılar",
                "easy": ["fatura no", "fatura tarihi", "tutar"],
                "hard": ["invoice number", "invoice date", "tax rate", "total amount", "customer name", "genel toplam"]
            }
        }
    
    def test_single_issue(self, issue_id: int, issue_data: dict) -> dict:
        """Tek issue'yu test et - Optimized"""
        print(f"\n🔍 Issue {issue_id}: {issue_data['description']}")
        
        all_test_cases = issue_data.get('easy', []) + issue_data.get('hard', [])
        total_tests = len(all_test_cases)
        
        detected_count = 0
        fixed_count = 0
        
        start_time = time.time()
        
        for i, test_case in enumerate(all_test_cases, 1):
            try:
                # Progress göster
                if i % 3 == 0 or i == total_tests:
                    print(f"   📊 İlerleme: {i}/{total_tests}")
                
                # Tespit
                detected_issues = self.processor.detect_issues(test_case, "test_column")
                detected = issue_id in detected_issues
                
                if detected:
                    detected_count += 1
                    
                    # Düzeltme
                    processed_value, fixes = self.processor.process_value(test_case, "test_column")
                    issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                    
                    if issue_fixes:
                        fixed_count += 1
                        
            except Exception as e:
                print(f"   ❌ Hata ({test_case}): {e}")
        
        execution_time = time.time() - start_time
        
        detection_rate = (detected_count / total_tests) * 100
        fix_rate = (fixed_count / total_tests) * 100
        
        result = {
            'total_tests': total_tests,
            'detected': detected_count,
            'fixed': fixed_count,
            'detection_rate': detection_rate,
            'fix_rate': fix_rate,
            'execution_time': execution_time
        }
        
        # Sonuç göster
        status = "✅" if detection_rate == 100 and fix_rate == 100 else "❌"
        print(f"   {status} Tespit: {detection_rate:5.1f}% | Düzeltme: {fix_rate:5.1f}% | Süre: {execution_time:.1f}s")
        
        return result
    
    def run_fast_tests(self):
        """Hızlı testleri çalıştır"""
        print("⚡ Hızlı Kapsamlı Test Başlıyor")
        print("=" * 60)
        print(f"🕐 Başlama zamanı: {datetime.now()}")
        print("🎯 Odak: Issue 21-26 (Yeni eklenen problemler)")
        
        self.start_time = time.time()
        
        test_cases = self.create_test_cases()
        
        # Issue'ları sırayla test et
        for issue_id in sorted(test_cases.keys()):
            result = self.test_single_issue(issue_id, test_cases[issue_id])
            self.test_results[issue_id] = result
        
        # Özet rapor
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """Özet rapor oluştur"""
        total_time = time.time() - self.start_time
        
        print(f"\n📊 HIZLI TEST SONUÇLARI")
        print("=" * 60)
        print(f"⏱️ Toplam süre: {total_time:.2f} saniye")
        
        # Genel istatistikler
        total_tests = sum(r['total_tests'] for r in self.test_results.values())
        total_detected = sum(r['detected'] for r in self.test_results.values())
        total_fixed = sum(r['fixed'] for r in self.test_results.values())
        
        overall_detection = (total_detected / total_tests) * 100
        overall_fix = (total_fixed / total_tests) * 100
        
        print(f"📝 Toplam test: {total_tests}")
        print(f"🔍 Toplam tespit: {total_detected} ({overall_detection:.1f}%)")
        print(f"🔧 Toplam düzeltme: {total_fixed} ({overall_fix:.1f}%)")
        
        # Issue bazında detay
        print(f"\n📋 Issue Bazında Detay:")
        perfect_issues = 0
        
        for issue_id in sorted(self.test_results.keys()):
            result = self.test_results[issue_id]
            status = "✅" if result['detection_rate'] == 100 and result['fix_rate'] == 100 else "❌"
            
            if result['detection_rate'] == 100 and result['fix_rate'] == 100:
                perfect_issues += 1
            
            print(f"Issue {issue_id:2d}: {status} Tespit: {result['detection_rate']:5.1f}% | "
                  f"Düzeltme: {result['fix_rate']:5.1f}% | Süre: {result['execution_time']:.1f}s")
        
        # Başarı değerlendirmesi
        print(f"\n🏆 Başarı Değerlendirmesi:")
        print(f"   📊 Mükemmel issue'lar: {perfect_issues}/{len(self.test_results)}")
        print(f"   📈 Genel tespit: {overall_detection:.1f}%")
        print(f"   📈 Genel düzeltme: {overall_fix:.1f}%")
        
        # Hafızamdaki kurala göre değerlendirme
        all_perfect = perfect_issues == len(self.test_results)
        overall_perfect = overall_detection == 100 and overall_fix == 100
        
        if all_perfect and overall_perfect:
            print(f"   🎉 TÜM ISSUE'LAR %100 BAŞARI!")
        else:
            print(f"   ⚠️ Bazı issue'lar %100'e çıkarılmalı")
            
            # Hangi issue'lar sorunlu?
            for issue_id, result in self.test_results.items():
                if result['detection_rate'] != 100 or result['fix_rate'] != 100:
                    print(f"      - Issue {issue_id}: Tespit {result['detection_rate']:.1f}%, Düzeltme {result['fix_rate']:.1f}%")
        
        print(f"\n⚡ Performans: {total_tests/total_time:.1f} test/saniye")

if __name__ == "__main__":
    # Hızlı test çalıştır
    fast_test = FastComprehensiveTest()
    fast_test.run_fast_tests()
