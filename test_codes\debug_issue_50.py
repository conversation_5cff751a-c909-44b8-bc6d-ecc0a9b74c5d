#!/usr/bin/env python3
"""
Issue 50 Debug - Satış Kanal Standart
Tespit: %0, Düzeltme: %0 - Hi<PERSON>mıyor!
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def debug_issue_50():
    """Issue 50'yi detaylı debug et"""
    
    print("🔍 Issue 50 Debug - Satış Kanal Standart")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri (paralel testten)
    test_cases = ["genel problem", "general issue"]
    
    # <PERSON>ha gerçekçi satış kanal test case'leri ekle
    sales_channel_test_cases = [
        "Online satış",
        "Store channel", 
        "Telefon",
        "E-commerce",
        "Mağaza",
        "Web sitesi",
        "Mobil uygulama",
        "Sosyal medya",
        "<PERSON>i",
        "Distribütör",
        "<PERSON><PERSON><PERSON><PERSON> sat<PERSON>",
        "Telemarketing"
    ]
    
    all_test_cases = test_cases + sales_channel_test_cases
    
    print(f"📝 Test case'leri: {len(all_test_cases)}")
    
    for i, test_case in enumerate(all_test_cases, 1):
        print(f"\n🧪 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 50 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 50]
            
            print(f"   🔧 Düzeltme: {'✅' if issue_fixes else '❌'} ({len(issue_fixes)} fix)")
            print(f"   📄 Processed: '{processed_value}'")
            
            if issue_fixes:
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            
            if not issue_detected:
                print(f"   ⚠️ PROBLEM: Issue 50 tespit edilmedi!")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")

if __name__ == "__main__":
    debug_issue_50()
