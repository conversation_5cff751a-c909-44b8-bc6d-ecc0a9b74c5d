#!/usr/bin/env python3
"""
Excel Dosyası İşleme Testi - 65 Issue ile Gerçek Excel Dosyası Test Et
"""

import pandas as pd
import numpy as np
import sys
import os

# Ana dizini path'e ekle
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from component_cards import ExcelDataProcessor, DataQualityProcessor, LLMClientFactory
import time
from datetime import datetime

def create_test_excel():
    """Test için örnek Excel dosyası oluştur"""
    print("📊 Test Excel dosyası oluşturuluyor...")
    
    # Von.md'deki 65 issue'yu içeren test verileri
    test_data = {
        'Gelir': ['1000 TL', '500 USD', '750 EUR', '2000 ₺', '1500 $'],
        'Tarih': ['25-12-2023', '2023/12/25', '01.01.2024', '15-03-2024', '2024-05-20'],
        'Müşter<PERSON>_Yorumu': ['Good quality', '<PERSON>yi kalite', 'Excellent service', 'Customer feedback', 'Product review'],
        'Lokasyon': ['TR', 'Turkey', 'Türkiye', 'İstanbul', 'Ankara'],
        'Telefon': ['+90 555 123 45 67', '0555 123 45 67', '5551234567', '+90-555-999-88-77', '555 444 33 22'],
        'Adres': ['<PERSON>kak No, Şehir, Ülke', 'İstanbul', '<PERSON>oğlu, İstanbul', 'Ankara, Türkiye', 'İzmir'],
        'Ürün_Kategori': ['Elektronik Kart', 'Electronic Card', 'Kart Elektronik', 'Digital Card', 'Elektronik'],
        'Fiyat': ['1,000.50', '1000,50', '2.500,75', '3,500.25', '4500,00'],
        'Miktar': ['10 kg', '10000 g', '5 adet', '3 kutu', '2 palet'],
        'Kimlik_No': ['***********', '123 456 789 01', '123-456-789-01', '12 345 678 901', '123.456.789.01'],
        'Müşteri_Tipi': ['Toptan', 'Bayi', 'Distribütör', 'Perakende', 'Kurumsal'],
        'Sözleşme_Koşul': ['ödeme koşulları esnektir', 'ödeme 30 gün içerisinde', 'Net 30 gün', 'esnek ödeme', 'hızlı teslimat'],
        'İndirim': ['%10 indirim', '500 TL indirim', '1000 TL', '%15 off', '250 USD discount'],
        'Ödeme_Türü': ['Kredi Kartı', 'Banka Transferi', 'Açık Hesap', 'Nakit', 'Credit Card'],
        'Eksik_Veri': ['N/A', 'NULL', '', 'missing', 'Veri Yok'],
        'Karakter_Sorunu': ['Ã¼rÃ¼n', 'Ä°stanbul', 'Ã§alÄ±ÅŸan', 'mÃ¼ÅŸteri', 'Ã¶rnek'],
        'Formül_Hatası': ['=SUM(A1:A10)', '#DIV/0!', '#VALUE!', '=VLOOKUP()', '#REF!'],
        'Boşluk_Sorunu': [' ABC Ltd. ', '  XYZ Şirketi  ', ' Müşteri Adı ', '  Ürün Kodu  ', ' Test Değeri '],
        'Yanlış_Hücre': ['Miktar kolonunda 100 TL', 'Fiyat kolonunda 5 adet', 'Tarih kolonunda ABC123', 'İsim kolonunda 12345', 'Kod kolonunda Ahmet'],
        'Çoklu_Bilgi': ['İstanbul, Ankara, İzmir', 'Email: <EMAIL>, Tel: 555-1234', 'Ahmet Yılmaz, 30 yaş', 'Ürün A, 100 TL, Stokta', 'Adres: İst, Tel: 555']
    }
    
    # DataFrame oluştur
    df = pd.DataFrame(test_data)
    
    # Excel dosyasına kaydet
    excel_path = 'test_codes/test_data_quality.xlsx'
    df.to_excel(excel_path, index=False, engine='openpyxl')
    
    print(f"✅ Test Excel dosyası oluşturuldu: {excel_path}")
    print(f"📊 Boyut: {df.shape[0]} satır, {df.shape[1]} kolon")
    print(f"📝 Toplam hücre: {df.shape[0] * df.shape[1]}")
    
    return excel_path, df

def test_excel_processing():
    """Excel dosyası işleme testini çalıştır"""
    print("🧪 EXCEL DOSYASI İŞLEME TESTİ")
    print("=" * 60)
    print("🎯 Hedef: 65 issue'yu gerçek Excel dosyasında test et")
    
    # 1. Test Excel dosyası oluştur
    excel_path, original_df = create_test_excel()
    
    # 2. ExcelDataProcessor oluştur
    from config import MAX_WORKERS
    print(f"\n🔧 ExcelDataProcessor başlatılıyor... (MAX_WORKERS: {MAX_WORKERS})")
    processor = ExcelDataProcessor(max_workers=MAX_WORKERS)  # Config'den MAX_WORKERS kullan
    
    # 3. Dosyayı işle
    output_path = 'test_codes/test_data_quality_fixed.xlsx'
    log_path = 'test_codes/test_data_quality_processing_log.xlsx'
    
    print(f"\n🚀 Excel dosyası işleniyor...")
    print(f"   📁 Girdi: {excel_path}")
    print(f"   📁 Çıktı: {output_path}")
    print(f"   📁 Log: {log_path}")
    
    start_time = time.time()
    
    try:
        # Ana işleme fonksiyonu - Streamlit olmadan
        summary = processor.process_excel_file(excel_path, output_path, log_path, use_streamlit=False)
        
        processing_time = time.time() - start_time
        
        print(f"\n✅ İşleme tamamlandı! ({processing_time:.2f} saniye)")
        
        # 4. Sonuçları analiz et
        print(f"\n📊 İŞLEME SONUÇLARI:")
        print(f"   📝 Toplam hücre: {summary['total_cells']}")
        print(f"   📊 Toplam satır: {summary['total_rows']}")
        print(f"   📊 Toplam kolon: {summary['total_columns']}")
        print(f"   🚨 Bulunan issue: {summary['issues_found']}")
        print(f"   🔧 Düzeltilen issue: {summary['issues_fixed']}")
        print(f"   ⏱️ İşleme süresi: {summary['processing_time_seconds']:.2f} saniye")
        print(f"   ⚡ Hücre/saniye: {summary['cells_per_second']:.1f}")
        print(f"   🔥 Paralel worker: {MAX_WORKERS} process (14 WORKER!)")
        print(f"   💻 CPU kullanımı: ~{MAX_WORKERS}x hızlanma (MAKSIMUM PARALEL!)")

        # 5. Log dosyasını analiz et
        if os.path.exists(log_path):
            log_df = pd.read_excel(log_path)
            print(f"\n📋 LOG ANALİZİ:")
            print(f"   📝 Toplam düzeltme: {len(log_df)}")
            
            # Issue bazında analiz
            issue_counts = log_df['issue_id'].value_counts().head(10)
            print(f"   🔝 En çok bulunan issue'lar:")
            for issue_id, count in issue_counts.items():
                issue_desc = processor.quality_processor.issues.ISSUES.get(issue_id, 'Bilinmeyen')
                print(f"      Issue {issue_id}: {count} adet - {issue_desc[:50]}...")
            
            # Kolon bazında analiz
            column_counts = log_df['column'].value_counts().head(5)
            print(f"   📊 En çok sorunlu kolonlar:")
            for column, count in column_counts.items():
                print(f"      {column}: {count} sorun")
        
        # 6. Dosya karşılaştırması
        if os.path.exists(output_path):
            fixed_df = pd.read_excel(output_path)
            print(f"\n🔍 DOSYA KARŞILAŞTIRMASI:")
            print(f"   📊 Orijinal: {original_df.shape}")
            print(f"   📊 Düzeltilmiş: {fixed_df.shape}")
            
            # Örnek değişiklikler
            print(f"\n📝 ÖRNEK DEĞİŞİKLİKLER:")
            for col in original_df.columns[:5]:  # İlk 5 kolon
                if col in fixed_df.columns:
                    orig_val = str(original_df[col].iloc[0])
                    fixed_val = str(fixed_df[col].iloc[0])
                    if orig_val != fixed_val:
                        print(f"   {col}: '{orig_val}' → '{fixed_val}'")
        
        return True
        
    except Exception as e:
        print(f"❌ İşleme hatası: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_cell_processing():
    """Tek hücre işleme testini çalıştır"""
    print("\n🔬 TEK HÜCRE İŞLEME TESTİ")
    print("=" * 40)
    
    # LLM client oluştur
    llm_client = LLMClientFactory.create_client_from_pool(1)
    processor = DataQualityProcessor(llm_client)
    
    # Test değerleri
    test_cases = [
        ('1000 TL', 'Gelir'),
        ('Good quality', 'Müşteri_Yorumu'),
        ('25-12-2023', 'Tarih'),
        ('', 'Eksik_Veri'),
        ('ödeme koşulları esnektir', 'Sözleşme_Koşul')
    ]
    
    for i, (value, column) in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{value}' (Kolon: {column})")
        
        # Tespit
        detected_issues = processor.detect_issues(value, column)
        print(f"   🔍 Tespit edilen issues: {detected_issues}")
        
        # Düzeltme
        if detected_issues:
            processed_value, fixes = processor.process_value(value, column)
            print(f"   🔧 Düzeltilmiş: '{processed_value}'")
            print(f"   📋 Uygulanan düzeltmeler: {len(fixes)}")
            
            for fix in fixes[:2]:  # İlk 2 düzeltme
                print(f"      - Issue {fix['issue_id']}: '{fix['original']}' → '{fix['fixed']}'")
        else:
            print(f"   ℹ️ Sorun tespit edilmedi")

if __name__ == "__main__":
    # 1. Tek hücre testi
    test_single_cell_processing()
    
    # 2. Tam Excel dosyası testi
    success = test_excel_processing()
    
    if success:
        print(f"\n🎉 TÜM TESTLER BAŞARILI!")
        print(f"✅ Excel dosyası işleme sistemi çalışıyor")
        print(f"✅ 65 issue tespit ve düzeltme aktif")
        print(f"✅ Paralel işleme sistemi çalışıyor")
    else:
        print(f"\n❌ TEST BAŞARISIZ!")
