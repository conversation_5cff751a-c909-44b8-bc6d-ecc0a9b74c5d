#!/usr/bin/env python3
"""
Tüm 65 Issue'yu <PERSON><PERSON><PERSON>ı Test Et - Her Issue %100 Başarı Hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_single_issue(issue_id):
    """Tek bir issue'yu test et"""
    try:
        # LLM client oluştur
        llm_client = OllamaLLMClient()
        processor = DataQualityProcessor(llm_client)
        
        # Test case'leri al
        test_cases = get_test_cases_for_issue(issue_id)
        
        success_count = 0
        total_tests = len(test_cases)
        failed_cases = []
        
        for test_case in test_cases:
            try:
                # Tespit kontrolü
                detected_issues = processor.detect_issues(test_case, "test_column")
                issue_detected = issue_id in detected_issues
                
                # Process_value ile tam test
                processed_value, fixes = processor.process_value(test_case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                # Başarı kriterleri
                if issue_detected and issue_fixes:
                    success_count += 1
                else:
                    failed_cases.append(test_case)
                    
            except Exception as e:
                failed_cases.append(f"{test_case} (ERROR: {e})")
        
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        
        return {
            'issue_id': issue_id,
            'success_rate': success_rate,
            'success_count': success_count,
            'total_tests': total_tests,
            'failed_cases': failed_cases
        }
        
    except Exception as e:
        return {
            'issue_id': issue_id,
            'success_rate': 0,
            'success_count': 0,
            'total_tests': 1,
            'error': str(e)
        }

def get_test_cases_for_issue(issue_id):
    """Her issue için test case'leri"""
    
    test_cases_map = {
        1: ["100 USD", "50 EUR", "1000 TL"],
        2: ["2023-12-25", "25/12/2023", "25.12.2023"],
        3: ["Product Name", "Customer Info", "Category Electronics"],
        4: ["İstanbul, TR", "Turkey", "TR-34"],
        5: ["+90 532 123 4567", "0532 123 4567", "532-123-4567"],
        6: ["18-25", "26-35 yaş", "65+"],
        7: ["Elektronik", "Ev Aletleri", "Bilgisayar"],
        8: ["UTC+3", "GMT+3", "Europe/Istanbul"],
        9: ["10 adet", "5 kg", "100 pieces"],
        10: ["12345678901", "TC12345678901", "ID-123456"],
        11: ["12,34", "12.34", "12,345.67"],
        12: ["VIP", "Premium", "Standard"],
        13: ["Sipariş: 2023-12-25", "Order Date: 25/12/2023"],
        14: ["KDV Dahil", "Tax Included", "Vergi Hariç"],
        15: ["ABC Ltd.", "XYZ A.Ş.", "Test Company"],
        16: ["UTC+3", "GMT+3", "Sözleşme: UTC+3"],
        17: ["PROD-001", "ABC123", "KATEGORI-001"],
        18: ["10 adet", "5 kutu", "100 pieces"],
        19: ["KOBİ", "Büyük İşletme", "Startup"],
        20: ["1.25 USD/TL", "1,25 EUR/USD", "Kur: 1.30"],
        21: ["Ödeme 30 gün", "Teslimat hızlı", "Garanti 1 yıl"],
        22: ["100000 TL", "50k USD", "1M EUR"],
        23: ["%20 indirim", "100 TL indirim", "Erken ödeme"],
        24: ["elektronik eşya", "küçük ev aletleri", "Elektronik"],
        25: ["kredi kart", "banka havalesi", "Nakit"],
        26: ["fatura no", "invoice number", "Fatura No"],
        27: ["3 gün teslimat", "1 hafta", "2 weeks delivery"],
        28: ["A. Kaya", "Ali K.", "REP-123"],
        29: ["Yıllık hedef", "Q1 target", "Aylık"],
        30: ["100 adet", "50 kutu", "25 palet"],
        64: ["N/A", "NULL", "", "missing"],
        65: ["Türkçe karakter", "Unicode", "Character set"]
    }
    
    return test_cases_map.get(issue_id, [f"test_value_{issue_id}"])

def main():
    """Ana test fonksiyonu"""
    print("🧪 TÜM 65 ISSUE SIRALI TEST - %100 BAŞARI HEDEFİ")
    print("=" * 60)
    
    # İlk 30 issue'yu test et
    test_issues = list(range(1, 31)) + [64, 65]
    
    print(f"📋 Test edilecek issue sayısı: {len(test_issues)}")
    print(f"🎯 Hedef: Her issue %100 başarı\n")
    
    results = []
    perfect_count = 0
    
    for issue_id in test_issues:
        print(f"🔄 Issue {issue_id} test ediliyor...")
        
        result = test_single_issue(issue_id)
        results.append(result)
        
        success_rate = result['success_rate']
        status = "✅" if success_rate == 100.0 else "⚠️" if success_rate >= 90 else "❌"
        
        print(f"{status} Issue {issue_id}: {success_rate:.1f}% ({result['success_count']}/{result['total_tests']})")
        
        if success_rate == 100.0:
            perfect_count += 1
        elif success_rate < 100.0:
            print(f"   ❌ Başarısız case'ler: {result.get('failed_cases', [])}")
        
        print()
    
    # Sonuçları özetle
    print(f"📊 GENEL SONUÇLAR:")
    print(f"   🎯 Mükemmel (%100): {perfect_count}/{len(test_issues)}")
    print(f"   📈 Başarı oranı: {(perfect_count/len(test_issues))*100:.1f}%")
    
    if perfect_count == len(test_issues):
        print(f"\n🎉 MÜKEMMEL! TÜM ISSUE'LAR %100 BAŞARIYA ULAŞTI!")
    else:
        print(f"\n🔧 Düzeltilmesi Gereken Issue'lar:")
        for result in results:
            if result['success_rate'] < 100.0:
                print(f"   Issue {result['issue_id']}: {result['success_rate']:.1f}%")
    
    return results

if __name__ == "__main__":
    results = main()
