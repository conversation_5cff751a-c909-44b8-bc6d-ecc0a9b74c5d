#!/usr/bin/env python3
"""
Issue 59 Test - Müşteri Şirket İsim Tutarsızlık Düzeltme
von.md'ye göre: Şirket isimlerinde tutarsızlık ve tekrarlar
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_59():
    """Issue 59'u kapsamlı test et"""
    
    print("🧪 Issue 59 Test - Müşteri Şirket İsim Tutarsızlık Düzeltme")
    print("=" * 65)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # Tekrarlı isimler
        "ABC Ltd. / ABC Limited",
        "XYZ Şti. XYZ",
        "DEF A.Ş. / DEF Anonim Şirketi",
        
        # Kısaltma karışıklıkları
        "GHI Inc. GHI",
        "JKL Corp / JKL Corporation",
        "MNO Şirketi / MNO",
        
        # Tekrar eden formatlar
        "PQR Ltd. PQR Ltd.",
        "STU A.Ş. STU",
        "VWX Inc. VWX Inc.",
        
        # Karışık formatlar
        "YZ Şti. / YZ Şirketi",
        "ABC Corp. ABC",
        "DEF Limited / DEF Ltd.",
        
        # Doğru formatlar (kontrol)
        "ABC Limited",
        "XYZ Şti."
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 59 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 59]
            
            # Doğru formatlar için tespit edilmemesi normal
            correct_formats = ["ABC Limited", "XYZ Şti."]
            is_correct_format = test_case in correct_formats
            
            if is_correct_format and not issue_detected:
                success_count += 1
                print(f"   ✅ BAŞARILI: Doğru format tespit edilmedi (normal)")
            elif issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 59 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 59 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_59()
