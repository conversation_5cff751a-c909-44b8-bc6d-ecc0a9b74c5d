#!/usr/bin/env python3
"""
Issue 43 Test - Yanlış Hücre Veri Düzeltme
von.md'ye göre: <PERSON><PERSON><PERSON><PERSON> hücreye girilen veriler
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_43():
    """Issue 43'ü kapsamlı test et"""
    
    print("🧪 Issue 43 Test - Yanlış Hücre Veri Düzeltme")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # Fiyat bilgisi yanlış yerde
        "Miktar kolonunda 100 TL",
        "Fiyat kolonunda 5 adet",
        
        # Tarih bilgisi yanlış yerde
        "Teslimat kolonunda 01.01.2024",
        "Müşteri adı kolonunda ABC123",
        
        # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yanl<PERSON><PERSON> hücre durumları
        "<PERSON>rih kolonunda 500 USD",
        "İsim kolonunda 15.03.2024",
        "Kod kolonunda Ahmet Yılmaz",
        
        # Açık yanlış hücre ifadeleri
        "yanlış hücre verisi",
        "wrong cell data",
        "hatalı kolon bilgisi",
        
        # Doğru hücre verileri (kontrol)
        "100 TL",  # Normal fiyat
        "5 adet",  # Normal miktar
        "01.01.2024"  # Normal tarih
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 43 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 43]
            
            # Doğru veriler için tespit edilmemesi normal
            correct_data = ["100 TL", "5 adet", "01.01.2024"]
            is_correct_data = test_case in correct_data
            
            if is_correct_data and not issue_detected:
                success_count += 1
                print(f"   ✅ BAŞARILI: Doğru veri tespit edilmedi (normal)")
            elif issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 43 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 43 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_43()
