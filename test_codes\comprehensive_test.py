#!/usr/bin/env python3
"""
Kapsamlı Test: Von.md'deki 65 Problemi Ayrı Ayrı Test Et
Her problem için kolay ve zor test case'leri
"""

import pandas as pd
import numpy as np
from component_cards import ExcelDataProcessor, DataQualityProcessor, OllamaLLMClient
import tempfile
import os
from datetime import datetime
import json

class ComprehensiveTestSuite:
    """65 problemi kapsamlı test eden sınıf"""
    
    def __init__(self):
        self.llm_client = OllamaLLMClient()
        self.processor = DataQualityProcessor(self.llm_client)
        self.test_results = {}
        
    def create_test_cases(self):
        """Her problem için kolay ve zor test case'leri olu<PERSON>"""
        
        test_cases = {
            # Problem 1-10: Temel format problemleri
            1: {
                "description": "Para birimi normalizasyonu",
                "easy": ["1000 TL", "500 USD", "750 EUR"],
                "hard": ["1,500.75 TL", "2.500,50 USD", "€ 1000", "₺1500", "$500.25"]
            },
            
            2: {
                "description": "Tarih format standardizasyonu",
                "easy": ["25-12-2023", "2023/12/25", "01.01.2024"],
                "hard": ["1 Ocak 2024", "Jan 1, 2024", "2024-12-25T10:30:00", "Pazartesi, 1 Ocak 2024", "Monday, Jan 1, 2024"]
            },

            3: {
                "description": "Dil standardizasyonu",
                "easy": ["Hello Merhaba", "Customer müşteri", "Product ürün"],
                "hard": ["Customer müşteri service hizmet", "Product ürün quality kalite", "Good İyi service hizmet", "Technology teknoloji computer bilgisayar"]
            },

            4: {
                "description": "Lokasyon standardizasyonu",
                "easy": ["Turkey", "Türkiye", "İstanbul"],
                "hard": ["İstanbul, Turkey", "Ankara/TR", "Turkey - İzmir", "TR-34", "Bursa, Türkiye"]
            },
            
            5: {
                "description": "Telefon format normalizasyonu",
                "easy": ["+90 555 123 45 67", "0555 123 45 67", "5551234567"],
                "hard": ["+90-************", "(0555) 123 45 67", "90 555 123 45 67"]
            },
            
            6: {
                "description": "Adres format standardizasyonu",
                "easy": ["Atatürk Cad. No:123", "Merkez Mah. 1. Sok.", "İstanbul, Kadıköy"],
                "hard": ["Atatürk Cad. No:123 Kat:5 Daire:10 Kadıköy/İstanbul", "Merkez Mah. Cumhuriyet Cad. No:45 Kat:2", "Bağdat Cad. Site Plaza Blok A Daire 15"]
            },

            7: {
                "description": "Ürün kategori normalizasyonu",
                "easy": ["Elektronik Eşya", "Ev Aletleri", "Beyaz Eşya"],
                "hard": ["Elektronik Eşya ve Aksesuarları", "Küçük Ev Aletleri", "Büyük Beyaz Eşya", "Ev Elektroniği"]
            },
            
            8: {
                "description": "Zaman dilimi normalizasyonu",
                "easy": ["UTC", "GMT", "EEST"],
                "hard": ["2024-01-01 10:30:00 UTC", "2024-01-01 13:30:00 +03:00"]
            },
            
            9: {
                "description": "Miktar birim normalizasyonu",
                "easy": ["5 kg", "10 g", "2 litre"],
                "hard": ["2.5 kilogram", "1500 gram", "0.5 lt", "500 ml"]
            },
            
            10: {
                "description": "Kimlik numarası format normalizasyonu",
                "easy": ["12345678901", "123 456 789 01"],
                "hard": ["123-456-789-01", "123.456.789.01", "TC: 12345678901"]
            },
            
            # Problem 11-20: Sayısal ve para problemleri
            11: {
                "description": "Ondalık işaret normalizasyonu",
                "easy": ["1,000.50", "1000,50", "2.500"],
                "hard": ["1,500.75", "2.500,25", "3,000.00", "4.500,50"]
            },
            
            12: {
                "description": "Müşteri kategori normalizasyonu",
                "easy": ["Toptan Müşteri", "Bayi Firma", "Distribütör Şirket"],
                "hard": ["Yetkili Bayi Müşterisi", "Bölge Distribütörü", "Kurumsal B2B Müşteri", "Perakende Satış"]
            },
            
            13: {
                "description": "Sipariş tarih format normalizasyonu",
                "easy": ["2024-01-01", "01/01/2024", "1-1-2024"],
                "hard": ["Pazartesi, 1 Ocak 2024", "Monday, Jan 1, 2024", "01-Jan-2024", "Salı, 15 Şubat 2024"]
            },
            
            14: {
                "description": "Vergi dahil/hariç fiyat normalizasyonu",
                "easy": ["1000 TL + KDV", "500 TL KDV Dahil", "750 TL Vergi Hariç"],
                "hard": ["1000 TL (KDV Hariç)", "500 TL (%18 KDV Dahil)", "750 TL + %18 Vergi", "2000 TL Tax Included"]
            },
            
            15: {
                "description": "Firma isim normalizasyonu",
                "easy": ["ABC Ltd.", "ABC Limited", "ABC Ltd. Şti."],
                "hard": ["ABC LİMİTED ŞİRKETİ", "A.B.C. Ltd. Şti.", "ABC LTD ŞTİ"]
            },
            
            16: {
                "description": "Sözleşme zaman dilimi normalizasyonu",
                "easy": ["UTC 10:00", "GMT+3 13:00", "TRT 14:00"],
                "hard": ["2024-01-01 10:00:00 UTC", "2024-01-01 13:00:00 +03:00", "10:30:00 GMT+3", "14:00 TRT"]
            },
            
            17: {
                "description": "Ürün kod standardizasyonu",
                "easy": ["P123", "PRO123", "Product_123"],
                "hard": ["PROD-123-A", "P_123_V2", "PRODUCT_CODE_123"]
            },
            
            18: {
                "description": "Sipariş adet birim normalizasyonu",
                "easy": ["5 kutu", "10 palet", "100 adet"],
                "hard": ["2.5 kutu", "1/2 palet", "100 adet (12'li paket)"]
            },
            
            19: {
                "description": "Şirket büyüklük kategori normalizasyonu",
                "easy": ["50 çalışan", "KOBİ firma", "Büyük şirket"],
                "hard": ["10M TL ciro", "Küçük İşletme (1-9 çalışan)", "500 personel", "Orta ölçekli işletme"]
            },
            
            20: {
                "description": "Para birimi kur normalizasyonu",
                "easy": ["1000 USD (kur)", "750 EUR + kur", "2000 TL kuru"],
                "hard": ["1000 USD (2024 kuru)", "750 EUR + kur farkı", "500 USD - eski kur", "1000 USD kuru 32.50"]
            },

            # Problem 21-26: Yeni eklenen sözleşme ve koşullar problemleri
            21: {
                "description": "Sözleşme ve sipariş koşulları",
                "easy": ["Net 30 gün", "Peşin ödeme", "1 yıl garanti"],
                "hard": ["net30gün", "peşinödeme", "1yılgaranti", "iadeedilmez", "exworks", "60günvade"]
            },

            22: {
                "description": "Kredi limitleri farklı birimlendirmeler",
                "easy": ["100000 TL", "50000 USD", "25000 EUR"],
                "hard": ["50k TL", "10k USD", "1M TL", "500 bin TL", "2 milyon USD", "100,000 TL", "50.5k EUR"]
            },

            23: {
                "description": "Kampanya ve indirim türleri",
                "easy": ["20% indirim", "100 TL indirim", "Erken ödeme"],
                "hard": ["%20 off", "100 lira indirim", "erkenödeme", "toplualım kampanyası", "sezon sonu %30", "yeni müşteri 50TL"]
            },

            24: {
                "description": "Ürün kategorilerinin standart olmaması",
                "easy": ["elektronik eşya", "ev aletleri", "beyaz eşya"],
                "hard": ["küçük ev aletleri", "büyük beyaz eşya", "giyim ve tekstil", "otomobil yedek parça", "spor malzemeleri ve fitness"]
            },

            25: {
                "description": "Ödeme türlerinde farklılık",
                "easy": ["kredi kart", "banka kartı", "nakit"],
                "hard": ["elektronik transfer", "banka havalesi", "taksitli ödeme", "cash on delivery", "credit card", "wire transfer"]
            },

            26: {
                "description": "Fatura detaylarında farklı yapılar",
                "easy": ["fatura no", "fatura tarihi", "tutar"],
                "hard": ["invoice number", "invoice date", "tax rate", "total amount", "customer name", "genel toplam"]
            }
        }

        return test_cases
    
    def test_single_issue(self, issue_id: int, test_data: dict) -> dict:
        """Tek bir problemi test et"""
        print(f"\n🔍 Problem {issue_id} Test Ediliyor: {test_data['description']}")
        
        results = {
            "issue_id": issue_id,
            "description": test_data['description'],
            "easy_tests": [],
            "hard_tests": [],
            "detection_success": 0,
            "fixing_success": 0,
            "total_tests": 0
        }
        
        # Kolay testler
        print("  📝 Kolay testler:")
        for i, test_value in enumerate(test_data['easy']):
            detected_issues = self.processor.detect_issues(test_value, f"test_column_{issue_id}")
            fixed_value, fixes = self.processor.process_value(test_value, f"test_column_{issue_id}")
            
            detection_success = issue_id in detected_issues
            fixing_success = len(fixes) > 0 and any(f['issue_id'] == issue_id for f in fixes)
            
            test_result = {
                "original": test_value,
                "detected_issues": detected_issues,
                "fixed": fixed_value,
                "fixes_applied": fixes,
                "detection_success": detection_success,
                "fixing_success": fixing_success
            }
            
            results['easy_tests'].append(test_result)
            results['total_tests'] += 1
            
            if detection_success:
                results['detection_success'] += 1
            if fixing_success:
                results['fixing_success'] += 1
                
            status = "✅" if detection_success else "❌"
            print(f"    {status} '{test_value}' -> {detected_issues}")
        
        # Zor testler
        print("  🔥 Zor testler:")
        for i, test_value in enumerate(test_data['hard']):
            detected_issues = self.processor.detect_issues(test_value, f"test_column_{issue_id}")
            fixed_value, fixes = self.processor.process_value(test_value, f"test_column_{issue_id}")
            
            detection_success = issue_id in detected_issues
            fixing_success = len(fixes) > 0 and any(f['issue_id'] == issue_id for f in fixes)
            
            test_result = {
                "original": test_value,
                "detected_issues": detected_issues,
                "fixed": fixed_value,
                "fixes_applied": fixes,
                "detection_success": detection_success,
                "fixing_success": fixing_success
            }
            
            results['hard_tests'].append(test_result)
            results['total_tests'] += 1
            
            if detection_success:
                results['detection_success'] += 1
            if fixing_success:
                results['fixing_success'] += 1
                
            status = "✅" if detection_success else "❌"
            print(f"    {status} '{test_value}' -> {detected_issues}")
        
        # Özet
        detection_rate = (results['detection_success'] / results['total_tests']) * 100
        fixing_rate = (results['fixing_success'] / results['total_tests']) * 100
        
        print(f"  📊 Tespit Başarısı: {results['detection_success']}/{results['total_tests']} ({detection_rate:.1f}%)")
        print(f"  🔧 Düzeltme Başarısı: {results['fixing_success']}/{results['total_tests']} ({fixing_rate:.1f}%)")
        
        return results

    def run_all_tests(self):
        """Tüm 26 problemi test et (güncellenmiş)"""
        print("🧪 Kapsamlı Test Süreci Başlıyor")
        print("=" * 60)
        print(f"Test zamanı: {datetime.now()}")
        print("Von.md dosyasındaki problemler ayrı ayrı test ediliyor...\n")

        test_cases = self.create_test_cases()

        # İlk 26 problemi test et (21-26 yeni eklenenler dahil)
        for issue_id in range(1, 27):
            if issue_id in test_cases:
                result = self.test_single_issue(issue_id, test_cases[issue_id])
                self.test_results[issue_id] = result
            else:
                print(f"⚠️ Problem {issue_id} için test case bulunamadı")

        # Özet rapor
        self.generate_summary_report()

    def generate_summary_report(self):
        """Özet rapor oluştur"""
        print("\n" + "=" * 60)
        print("📊 ÖZET RAPOR")
        print("=" * 60)

        total_issues = len(self.test_results)
        total_tests = sum(r['total_tests'] for r in self.test_results.values())
        total_detections = sum(r['detection_success'] for r in self.test_results.values())
        total_fixes = sum(r['fixing_success'] for r in self.test_results.values())

        print(f"Test edilen problem sayısı: {total_issues}")
        print(f"Toplam test case sayısı: {total_tests}")
        print(f"Başarılı tespit: {total_detections}/{total_tests} ({(total_detections/total_tests)*100:.1f}%)")
        print(f"Başarılı düzeltme: {total_fixes}/{total_tests} ({(total_fixes/total_tests)*100:.1f}%)")

        # En başarılı problemler
        print("\n🏆 EN BAŞARILI PROBLEMLER:")
        sorted_results = sorted(
            self.test_results.items(),
            key=lambda x: (x[1]['detection_success'] / x[1]['total_tests']),
            reverse=True
        )

        for issue_id, result in sorted_results[:5]:
            rate = (result['detection_success'] / result['total_tests']) * 100
            print(f"  {issue_id}. {result['description']}: {rate:.1f}%")

        # En zor problemler
        print("\n🔥 EN ZOR PROBLEMLER:")
        for issue_id, result in sorted_results[-5:]:
            rate = (result['detection_success'] / result['total_tests']) * 100
            print(f"  {issue_id}. {result['description']}: {rate:.1f}%")

        # Sonuçları dosyaya kaydet
        self.save_results()

    def save_results(self):
        """Test sonuçlarını dosyaya kaydet"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_results_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n💾 Test sonuçları kaydedildi: {filename}")

def main():
    """Ana test fonksiyonu"""
    try:
        # LLM bağlantısını test et
        print("🤖 LLM bağlantısı kontrol ediliyor...")
        llm_client = OllamaLLMClient()
        test_response = llm_client.generate("Test", use_cache=False)

        if not test_response:
            print("❌ LLM bağlantısı başarısız! Ollama çalışıyor mu?")
            return

        print("✅ LLM bağlantısı başarılı")

        # Kapsamlı testleri çalıştır
        test_suite = ComprehensiveTestSuite()
        test_suite.run_all_tests()

        print("\n🎉 Tüm testler tamamlandı!")

    except Exception as e:
        print(f"❌ Test hatası: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
