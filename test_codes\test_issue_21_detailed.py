#!/usr/bin/env python3
"""
Issue 21 (Sözleşme Koşulları) Detaylı Analiz
Hangi test case'lerin başarısız olduğunu görelim
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_21_detailed():
    """Issue 21'i detaylı analiz et"""
    
    print("🧪 Issue 21 (Sözleşme Koşulları) Detaylı Analiz")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Paralel test'teki gerçek test case'leri
    test_cases = [
        "ödeme koşulları esnektir",
        "ödeme 30 gün içerisinde yapılır", 
        "Net 30 gün",
        "Peşin ödeme",
        "esnek ödeme",
        "30 günde ödeme"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_21_detected = 21 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_21_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_21_fixes = [f for f in fixes if f['issue_id'] == 21]
            
            if issue_21_detected and issue_21_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_21_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_21_detected and not issue_21_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_21_detected}, Düzeltme={len(issue_21_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 21 Detaylı Sonuçlar:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    return success_rate

if __name__ == "__main__":
    test_issue_21_detailed()
