#!/usr/bin/env python3
"""
LLM Veri Ön İşleme Uygulaması Test Dosyası
Von.md dosyasındaki 65 problemi test eder
"""

import pandas as pd
import numpy as np
from component_cards import ExcelDataProcessor, DataQualityProcessor, OllamaLLMClient
import tempfile
import os
from datetime import datetime

def create_test_data():
    """Test verisi oluştur - von.md problemlerini içeren"""
    
    test_data = {
        'Gelir': [
            '1000 TL',           # Problem 1: Para birimi karışık
            '500 USD',
            '750 EUR',
            '2,500.50 TL',
            '1.000,75 USD'       # Problem 11: Ondalık işaret
        ],
        'Tarih': [
            '25-12-2023',        # Problem 2: Tarih format
            '2023/12/25',
            '01.01.2024',        # Problem 44: Farklı format
            'Daha sonra belirlenecek',
            '1 Ocak 2024'
        ],
        'Telefon': [
            '+90 555 123 45 67', # Problem 5: Telefon format
            '0555 123 45 67',
            '5551234567',
            '+90-************',
            ' 0555 123 45 67 '   # Problem 49: <PERSON><PERSON><PERSON>
        ],
        'Müşteri_Adı': [
            ' ABC Ltd. ',        # Problem 49: Boşluk
            'XYZ Şirketi',
            'DEF Limited',       # Problem 15: Firma isim farklılık
            'ABC Ltd. Şti.',
            'GHI A.Ş.'
        ],
        'Miktar': [
            '5 adet',            # Problem 45: Sayı-metin karışık
            '10 kg',             # Problem 9: Miktar birim
            '2.5 litre',
            '100 gram',
            '15 kutu'            # Problem 18: Sipariş adet birim
        ],
        'Fiyat': [
            '1,000.50',          # Problem 11: Ondalık işaret
            '2000,75',           # Problem 51: Virgül nokta karışık
            '3.500',
            '4,250.00',
            '5000'
        ],
        'Lokasyon': [
            'TR',                # Problem 4: Lokasyon format
            'Turkey',
            'İstanbul',
            'Ankara, Turkey',
            'İzmir, TR'
        ],
        'Kampanya': [
            '%10 indirim',       # Problem 23: İndirim tip
            '500 TL indirim',    # Problem 47: Sayı-metin karışık
            '15% off',
            '1000 TL',
            '%25'
        ],
        'Çoklu_Bilgi': [
            'İstanbul, Ankara, İzmir',  # Problem 46: Çoklu bilgi
            '5 ürün, 2 yedek parça',
            'A, B, C kategorisi',
            'Pazartesi, Çarşamba',
            'Sabah, Öğle, Akşam'
        ],
        'Yazım_Hatası': [
            'Elektronik Kart',   # Problem 42: Yazım hatası
            'Elktronik Kart',
            'Bilgisayar',
            'Bilgisyar',
            'Telefon'
        ]
    }
    
    return pd.DataFrame(test_data)

def test_issue_detection():
    """Sorun tespit fonksiyonunu test et"""
    print("🔍 Sorun tespit testi başlıyor...")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test verileri
    test_cases = [
        ('1000 TL', 'Gelir', [1, 20]),
        ('25-12-2023', 'Tarih', [2, 13, 44]),
        ('+90 555 123 45 67', 'Telefon', [5]),
        ('1,000.50', 'Fiyat', [11, 51]),
        (' ABC Ltd. ', 'Müşteri', [49]),
        ('5 adet', 'Miktar', [45, 47, 50]),
        ('İstanbul, Ankara, İzmir', 'Lokasyon', [46])
    ]
    
    for value, column, expected_issues in test_cases:
        detected = processor.detect_issues(value, column)
        print(f"Değer: '{value}' -> Tespit edilen: {detected}")
        
        # En az bir beklenen sorun tespit edildi mi?
        found_expected = any(issue in detected for issue in expected_issues)
        if found_expected:
            print(f"✅ Başarılı: {value}")
        else:
            print(f"❌ Başarısız: {value} - Beklenen: {expected_issues}")
    
    print("✅ Sorun tespit testi tamamlandı\n")

def test_data_processing():
    """Veri işleme fonksiyonunu test et"""
    print("🔧 Veri işleme testi başlıyor...")
    
    # Test verisi oluştur
    df = create_test_data()
    print(f"Test verisi oluşturuldu: {df.shape[0]} satır, {df.shape[1]} kolon")
    
    # Geçici dosya oluştur
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        df.to_excel(tmp_file.name, index=False)
        input_path = tmp_file.name
    
    output_path = 'test_output_fixed.xlsx'
    log_path = 'test_processing_log.xlsx'
    
    try:
        # İşlemciyi başlat
        processor = ExcelDataProcessor()
        
        # Veriyi işle
        print("🚀 İşleme başlıyor...")
        summary = processor.process_excel_file(input_path, output_path, log_path)
        
        # Sonuçları göster
        print("\n📊 İşleme Raporu:")
        print(f"Toplam satır: {summary['total_rows']}")
        print(f"Toplam kolon: {summary['total_columns']}")
        print(f"Toplam hücre: {summary['total_cells']}")
        print(f"Bulunan sorun: {summary['issues_found']}")
        print(f"Düzeltilen sorun: {summary['issues_fixed']}")
        print(f"İşleme süresi: {summary['processing_time_seconds']:.2f} saniye")
        print(f"Hız: {summary['cells_per_second']:.1f} hücre/saniye")
        
        # Çıktı dosyalarını kontrol et
        if os.path.exists(output_path):
            print(f"✅ Çıktı dosyası oluşturuldu: {output_path}")
            
            # İşlenmiş veriyi yükle ve göster
            processed_df = pd.read_excel(output_path)
            print("\n📋 İşlenmiş veri örneği:")
            print(processed_df.head())
            
        else:
            print(f"❌ Çıktı dosyası oluşturulamadı: {output_path}")
        
        if os.path.exists(log_path):
            print(f"✅ Log dosyası oluşturuldu: {log_path}")
            
            # Log dosyasını yükle ve göster
            log_df = pd.read_excel(log_path)
            print(f"\n📋 İşleme logu: {len(log_df)} düzeltme")
            if not log_df.empty:
                print(log_df[['issue_id', 'issue_description', 'original', 'fixed', 'column']].head())
        else:
            print(f"ℹ️ Log dosyası oluşturulmadı (düzeltme yapılmadı): {log_path}")
        
        print("✅ Veri işleme testi tamamlandı")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        
    finally:
        # Geçici dosyaları temizle
        for file_path in [input_path, output_path, log_path]:
            if os.path.exists(file_path):
                try:
                    os.unlink(file_path)
                    print(f"🗑️ Temizlendi: {file_path}")
                except:
                    pass

def test_llm_connection():
    """LLM bağlantısını test et"""
    print("🤖 LLM bağlantı testi başlıyor...")
    
    try:
        client = OllamaLLMClient()
        
        # Basit test
        response = client.generate("Test mesajı", use_cache=False)
        
        if response and response.strip():
            print(f"✅ LLM bağlantısı başarılı")
            print(f"Model yanıtı: {response[:100]}...")
        else:
            print("❌ LLM yanıt vermiyor")
            
    except Exception as e:
        print(f"❌ LLM bağlantı hatası: {e}")
    
    print("✅ LLM bağlantı testi tamamlandı\n")

def main():
    """Ana test fonksiyonu"""
    print("🧪 LLM Veri Ön İşleme Uygulaması Test Süreci")
    print("=" * 50)
    print(f"Test zamanı: {datetime.now()}")
    print(f"Von.md dosyasındaki 65 problem test ediliyor...\n")
    
    # Testleri sırayla çalıştır
    test_llm_connection()
    test_issue_detection()
    test_data_processing()
    
    print("\n🎉 Tüm testler tamamlandı!")
    print("=" * 50)

if __name__ == "__main__":
    main()
