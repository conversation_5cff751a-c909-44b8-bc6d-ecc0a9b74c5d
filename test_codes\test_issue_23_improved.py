#!/usr/bin/env python3
"""
Issue 23 (<PERSON><PERSON><PERSON><PERSON>) LLM İyileştirme Testi
Von.md'ye uygun test case'leri ile %100 başarı hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_23_improved():
    """Issue 23'ü LLM iyileştirmesi ile test et"""
    
    print("🧪 Issue 23 (Kampanya İndirim) LLM İyileştirme Testi")
    print("=" * 60)
    print("🎯 Hedef: %100 başarı - Von.md'ye uygun test case'leri")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Von.md'ye uygun test case'leri - Kampanya indirim farklı türler
    test_cases = [
        "%10 indirim",              # Yüzde format
        "500 TL indirim",           # TL format
        "10 percent off",           # İngilizce yüzde
        "500 TL off",               # İngilizce TL
        "%15",                      # <PERSON><PERSON><PERSON> yüzde
        "1000 TL"                   # Sadece tutar
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_23_detected = 23 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_23_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_23_fixes = [f for f in fixes if f['issue_id'] == 23]
            
            if issue_23_detected and issue_23_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_23_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_23_detected and not issue_23_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_23_detected}, Düzeltme={len(issue_23_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 23 Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 Issue 23 %100 BAŞARI!")
        return True
    else:
        print(f"   ⚠️ Issue 23 henüz %100 değil")
        return False

if __name__ == "__main__":
    success = test_issue_23_improved()
    
    if success:
        print("\n🎯 Issue 23 %100 başarıya ulaştı!")
        print("   ➡️ Issue 24'e geçilebilir")
    else:
        print("\n⚠️ Issue 23 daha fazla iyileştirme gerekiyor")
        print("   ➡️ LLM prompt'larını güçlendir")
