#!/usr/bin/env python3
"""
Issue 7 (<PERSON><PERSON><PERSON>n Kategori İsimlendirme) LLM İyileştirme Testi
Von.md'ye uygun test case'leri ile %100 başarı hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_7_improved():
    """Issue 7'yi LLM iyileştirmesi ile test et"""
    
    print("🧪 Issue 7 (Ürün Kategori İsimlendirme) LLM İyileştirme Testi")
    print("=" * 60)
    print("🎯 Hedef: %100 başarı - Von.md'ye uygun test case'leri")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Von.md'ye uygun test case'leri - Ürün kategori farklı isimlendirme
    test_cases = [
        'Elektronik Kart',       # Farklı isimlendirme
        'Electronic Card',       # İngilizce
        'Kart Elektronik',       # Ters sıralama
        'Elektronik Kartı',      # <PERSON>k hali
        'E-Kart',               # Kısaltma
        'Digital Card'          # İngilizce farklı
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_7_detected = 7 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_7_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_7_fixes = [f for f in fixes if f['issue_id'] == 7]
            
            if issue_7_detected and issue_7_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_7_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_7_detected and not issue_7_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_7_detected}, Düzeltme={len(issue_7_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 7 Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 Issue 7 %100 BAŞARI!")
        return True
    else:
        print(f"   ⚠️ Issue 7 henüz %100 değil")
        return False

if __name__ == "__main__":
    success = test_issue_7_improved()
    
    if success:
        print("\n🎯 Issue 7 %100 başarıya ulaştı!")
        print("   ➡️ Issue 10'a geçilebilir")
    else:
        print("\n⚠️ Issue 7 daha fazla iyileştirme gerekiyor")
        print("   ➡️ LLM prompt'larını güçlendir")
