#!/usr/bin/env python3
"""
Issue 49 Debug - Ürün Fiyat Bilgisi
Tespit: %50, <PERSON><PERSON>zeltme: %50 - Ya<PERSON><PERSON> çalışıyor
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def debug_issue_49():
    """Issue 49'u detaylı debug et"""
    
    print("🔍 Issue 49 Debug - Ürün Fiyat Bilgisi")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri (paralel testten)
    test_cases = ["Product price", "Ürün fiyat"]
    
    # Daha gerçekçi fiyat test case'leri ekle
    price_test_cases = [
        "100 TL",
        "50 USD", 
        "25.50 EUR",
        "Fiyat: 100 TL",
        "Price: $50",
        "Cost: 25 EUR",
        "Amount: 100",
        "Total: 500 TL",
        "Tutar: 250 TL",
        "Miktar: 75 USD",
        "Ücret: 30 TL"
    ]
    
    all_test_cases = test_cases + price_test_cases
    
    print(f"📝 Test case'leri: {len(all_test_cases)}")
    
    for i, test_case in enumerate(all_test_cases, 1):
        print(f"\n🧪 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 49 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 49]
            
            print(f"   🔧 Düzeltme: {'✅' if issue_fixes else '❌'} ({len(issue_fixes)} fix)")
            print(f"   📄 Processed: '{processed_value}'")
            
            if issue_fixes:
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            
            if not issue_detected:
                print(f"   ⚠️ PROBLEM: Issue 49 tespit edilmedi!")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")

if __name__ == "__main__":
    debug_issue_49()
