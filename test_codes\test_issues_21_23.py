#!/usr/bin/env python3
"""
Issue 21-23 (<PERSON><PERSON><PERSON><PERSON><PERSON> ve <PERSON>şullar) test scripti
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issues_21_23():
    """Issue 21-23 sözleşme ve koşullar problemlerini test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri
    test_cases = {
        21: [  # Sözleşme ve sipariş koşulları
            # Kolay testler
            "Net 30 gün",
            "Peşin ödeme",
            "1 yıl garanti",
            
            # Zor testler
            "net30gün",
            "peşinödeme",
            "1yılgaranti",
            "iadeedilmez",
            "exworks",
            "60günvade"
        ],
        
        22: [  # Kredi limitleri farklı birimlendirmeler
            # Kolay testler
            "100000 TL",
            "50000 USD",
            "25000 EUR",
            
            # Zor testler
            "50k TL",
            "10k USD",
            "1M TL",
            "500 bin TL",
            "2 milyon USD",
            "100,000 TL",
            "50.5k EUR"
        ],
        
        23: [  # Kampanya ve indirim türleri
            # Kolay testler
            "20% indirim",
            "100 TL indirim",
            "Erken ödeme",
            
            # Zor testler
            "%20 off",
            "100 lira indirim",
            "erkenödeme",
            "toplualım kampanyası",
            "sezon sonu %30",
            "yeni müşteri 50TL"
        ]
    }
    
    print("🧪 Issue 21-23 (Sözleşme ve Koşullar) Test")
    print("=" * 60)
    
    total_tests = 0
    total_detected = 0
    total_fixed = 0
    
    for issue_id, test_values in test_cases.items():
        print(f"\n🔍 Issue {issue_id} Test Ediliyor")
        print("-" * 40)
        
        issue_detected = 0
        issue_fixed = 0
        
        for i, test_value in enumerate(test_values, 1):
            print(f"\n📝 Test {i}/{len(test_values)}: '{test_value}'")
            
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_value, "test_column")
            issue_detected_flag = issue_id in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected_flag else '❌'} (Issues: {detected_issues})")
            
            if issue_detected_flag:
                issue_detected += 1
                
                # 2. Process_value ile tam test
                try:
                    processed_value, fixes = processor.process_value(test_value, "test_column")
                    
                    # Issue düzeltmesi uygulandı mı?
                    issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                    if issue_fixes:
                        issue_fixed += 1
                        print(f"   ✅ Process_value düzeltmesi: '{processed_value}'")
                        for fix in issue_fixes:
                            print(f"      - {fix['original']} → {fix['fixed']}")
                    else:
                        print(f"   ❌ Process_value düzeltmesi uygulanmadı")
                        
                except Exception as e:
                    print(f"   ❌ Process_value hatası: {e}")
            
            total_tests += 1
        
        total_detected += issue_detected
        total_fixed += issue_fixed
        
        print(f"\n📊 Issue {issue_id} Sonuçları:")
        print(f"   📝 Test sayısı: {len(test_values)}")
        print(f"   🔍 Tespit edilen: {issue_detected}")
        print(f"   🔧 Düzeltilen: {issue_fixed}")
        print(f"   📈 Tespit oranı: {(issue_detected/len(test_values))*100:.1f}%")
        print(f"   📈 Düzeltme oranı: {(issue_fixed/issue_detected)*100:.1f}%" if issue_detected > 0 else "   📈 Düzeltme oranı: 0%")
    
    print(f"\n" + "=" * 60)
    print(f"📊 Genel Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   🔍 Toplam tespit: {total_detected}")
    print(f"   🔧 Toplam düzeltme: {total_fixed}")
    print(f"   📈 Genel tespit oranı: {(total_detected/total_tests)*100:.1f}%")
    print(f"   📈 Genel düzeltme oranı: {(total_fixed/total_detected)*100:.1f}%" if total_detected > 0 else "   📈 Genel düzeltme oranı: 0%")
    
    # Başarı değerlendirmesi
    detection_success = (total_detected >= total_tests * 0.7)  # %70 tespit başarısı
    fix_success = (total_fixed >= total_detected * 0.8)  # %80 düzeltme başarısı
    
    print(f"\n🏆 Başarı Değerlendirmesi:")
    print(f"   🔍 Tespit başarısı: {'✅' if detection_success else '❌'}")
    print(f"   🔧 Düzeltme başarısı: {'✅' if fix_success else '❌'}")
    
    if detection_success and fix_success:
        print(f"   🎉 Issue 21-23 implementasyonu başarılı!")
    else:
        print(f"   ⚠️ Issue 21-23 implementasyonu iyileştirme gerekiyor")

if __name__ == "__main__":
    test_issues_21_23()
