#!/usr/bin/env python3
"""
Basit Issue Test - Tek tek test et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_21():
    """Issue 21'i basit test et"""
    print("🧪 Issue 21 Test")
    
    try:
        llm_client = OllamaLLMClient()
        processor = DataQualityProcessor(llm_client)
        
        test_cases = ["1yılgaranti", "iadeedilmez", "60günvade"]
        
        for test_case in test_cases:
            print(f"   Test: '{test_case}'")
            detected = processor.detect_issues(test_case, "test")
            print(f"   Sonuç: {21 in detected}")
            
    except Exception as e:
        print(f"   Hata: {e}")

def test_issue_22():
    """Issue 22'yi basit test et"""
    print("\n🧪 Issue 22 Test")
    
    try:
        llm_client = OllamaLLMClient()
        processor = DataQualityProcessor(llm_client)
        
        test_cases = ["100000 TL", "50000 USD", "25000 EUR"]
        
        for test_case in test_cases:
            print(f"   Test: '{test_case}'")
            detected = processor.detect_issues(test_case, "test")
            print(f"   Sonuç: {22 in detected}")
            
    except Exception as e:
        print(f"   Hata: {e}")

if __name__ == "__main__":
    print("🎯 Basit Issue Test")
    print("=" * 30)
    
    test_issue_21()
    test_issue_22()
    
    print("\n✅ Test tamamlandı")
