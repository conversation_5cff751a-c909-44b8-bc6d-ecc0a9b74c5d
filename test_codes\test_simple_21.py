#!/usr/bin/env python3
"""
Issue 21 Basit Test
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_simple_21():
    """Issue 21'i basit test et"""
    
    print("🧪 Issue 21 Basit Test")
    print("=" * 40)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Basit test case'leri
    test_cases = ["Ödeme 30 gün", "Teslimat hızlı"]
    
    for test_case in test_cases:
        print(f"\nTest: '{test_case}'")
        
        # Tespit kontrolü
        detected_issues = processor.detect_issues(test_case, "test_column")
        print(f"Tespit edilen issues: {detected_issues}")
        
        # Process_value ile tam test
        processed_value, fixes = processor.process_value(test_case, "test_column")
        print(f"Processed değer: '{processed_value}'")
        
        # Issue 21 kontrolü
        issue_21_fixes = [f for f in fixes if f['issue_id'] == 21]
        print(f"Issue 21 fixes: {len(issue_21_fixes)}")
        
        if 21 in detected_issues:
            print("✅ Issue 21 tespit edildi")
        else:
            print("❌ Issue 21 tespit edilmedi")

if __name__ == "__main__":
    test_simple_21()
