#!/usr/bin/env python3
"""
Tüm Issue'ları Hızlı Test Et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import time

def test_single_issue(issue_id):
    """Tek bir issue'yu test et"""
    try:
        # LLM client oluştur
        llm_client = OllamaLLMClient()
        processor = DataQualityProcessor(llm_client)
        
        # Issue'ya göre test case'leri
        test_cases = {
            3: ["Product Name", "Customer Info", "Category Electronics"],
            6: ["18-25", "26-35 yaş", "65+"],
            64: ["N/A", "NULL", "", "missing"],
            21: ["Ödeme 30 gün", "Teslimat hızlı"],
            25: ["Kredi kartı", "Nakit ödeme"]
        }
        
        # Test case'leri al
        cases = test_cases.get(issue_id, [f"test_value_{issue_id}"])
        
        success_count = 0
        total_tests = len(cases)
        
        for test_case in cases:
            try:
                # Tespit kontrolü
                detected_issues = processor.detect_issues(test_case, "test_column")
                issue_detected = issue_id in detected_issues
                
                # Process_value ile tam test
                processed_value, fixes = processor.process_value(test_case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                if issue_detected and issue_fixes:
                    success_count += 1
                elif not issue_detected and issue_id in [3, 6, 64, 21, 25]:
                    # Bu issue'lar için tespit edilmemesi sorun
                    pass
                else:
                    success_count += 1
                    
            except Exception as e:
                print(f"Issue {issue_id} test hatası: {e}")
        
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        return issue_id, success_rate, success_count, total_tests
        
    except Exception as e:
        print(f"Issue {issue_id} genel hatası: {e}")
        return issue_id, 0, 0, 1

def main():
    """Ana test fonksiyonu"""
    print("🧪 Tüm Issue'ları Hızlı Test")
    print("=" * 50)
    
    # Test edilecek issue'lar (düşük performanslı olanlar öncelikli)
    priority_issues = [3, 6, 21, 25, 64]  # Düşük performanslı
    other_issues = list(range(1, 66))  # Tüm issue'lar
    
    # Öncelikli issue'ları test et
    print("🔥 Öncelikli Issue'lar:")
    for issue_id in priority_issues:
        issue_id, success_rate, success_count, total_tests = test_single_issue(issue_id)
        status = "✅" if success_rate >= 90 else "⚠️" if success_rate >= 70 else "❌"
        print(f"   {status} Issue {issue_id}: {success_rate:.1f}% ({success_count}/{total_tests})")
    
    print(f"\n📊 Öncelikli Issue'lar Tamamlandı!")
    print(f"   🎯 Issue 3, 6, 64 %100 olmalı")
    print(f"   🔧 Issue 21, 25 geliştirilmeli")

if __name__ == "__main__":
    main()
