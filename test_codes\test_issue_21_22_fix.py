#!/usr/bin/env python3
"""
Issue 21 ve 22'yi %100 başarı için test et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_21_fix():
    """Issue 21'i %100 başarı için test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Başarısız olan test case'leri
    failing_cases = [
        '1yılgaranti',
        'iadeedilmez', 
        '60günvade'
    ]
    
    print("🧪 Issue 21 (Sözleşme Koşulları) %100 Başarı Testi")
    print("=" * 60)
    
    success_count = 0
    
    for i, test_value in enumerate(failing_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_21_detected = 21 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_21_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_21_detected:
            # 2. Process_value ile tam test
            try:
                processed_value, fixes = processor.process_value(test_value, "test_column")
                
                # Issue 21 düzeltmesi uygulandı mı?
                issue_21_fixes = [f for f in fixes if f['issue_id'] == 21]
                if issue_21_fixes:
                    success_count += 1
                    print(f"   ✅ Başarılı: '{processed_value}'")
                    for fix in issue_21_fixes:
                        print(f"      - {fix['original']} → {fix['fixed']}")
                else:
                    print(f"   ❌ Düzeltme uygulanmadı")
                    
            except Exception as e:
                print(f"   ❌ Hata: {e}")
        else:
            print(f"   ❌ Tespit başarısız")
    
    print(f"\n📊 Issue 21 Sonuçları:")
    print(f"   📝 Test sayısı: {len(failing_cases)}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {(success_count/len(failing_cases))*100:.1f}%")
    
    return success_count == len(failing_cases)

def test_issue_22_fix():
    """Issue 22'yi %100 başarı için test et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Başarısız olan test case'leri
    failing_cases = [
        '100000 TL',
        '50000 USD',
        '25000 EUR'
    ]
    
    print("\n🧪 Issue 22 (Kredi Limitleri) %100 Başarı Testi")
    print("=" * 60)
    
    success_count = 0
    
    for i, test_value in enumerate(failing_cases, 1):
        print(f"\n📝 Test {i}: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        issue_22_detected = 22 in detected_issues
        
        print(f"   🔍 Tespit: {'✅' if issue_22_detected else '❌'} (Issues: {detected_issues})")
        
        if issue_22_detected:
            # 2. Process_value ile tam test
            try:
                processed_value, fixes = processor.process_value(test_value, "test_column")
                
                # Issue 22 düzeltmesi uygulandı mı?
                issue_22_fixes = [f for f in fixes if f['issue_id'] == 22]
                if issue_22_fixes:
                    success_count += 1
                    print(f"   ✅ Başarılı: '{processed_value}'")
                    for fix in issue_22_fixes:
                        print(f"      - {fix['original']} → {fix['fixed']}")
                else:
                    print(f"   ❌ Düzeltme uygulanmadı")
                    
            except Exception as e:
                print(f"   ❌ Hata: {e}")
        else:
            print(f"   ❌ Tespit başarısız")
    
    print(f"\n📊 Issue 22 Sonuçları:")
    print(f"   📝 Test sayısı: {len(failing_cases)}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {(success_count/len(failing_cases))*100:.1f}%")
    
    return success_count == len(failing_cases)

if __name__ == "__main__":
    print("🎯 Issue 21-22 %100 Başarı Testi")
    print("=" * 60)
    
    # Issue 21'i test et
    issue_21_success = test_issue_21_fix()
    
    # Issue 22'yi test et
    issue_22_success = test_issue_22_fix()
    
    print(f"\n" + "=" * 60)
    print(f"🏆 Final Sonuçları:")
    print(f"   Issue 21: {'✅ %100' if issue_21_success else '❌ %100 değil'}")
    print(f"   Issue 22: {'✅ %100' if issue_22_success else '❌ %100 değil'}")
    
    if issue_21_success and issue_22_success:
        print(f"   🎉 Her iki issue da %100 başarıya ulaştı!")
    else:
        print(f"   ⚠️ Daha fazla iyileştirme gerekli")
