#!/usr/bin/env python3
"""
Gerçek test.xlsx Dosyası ile Detaylı Test
"""

import pandas as pd
import sys
import os

# Ana dizini path'e ekle
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from component_cards import ExcelDataProcessor, DataQualityProcessor, LLMClientFactory
from config import MAX_WORKERS
import time

def analyze_test_excel():
    """test.xlsx dosyasını analiz et"""
    print("📊 TEST.XLSX DOSYASI ANALİZİ")
    print("=" * 60)
    
    try:
        # Excel dosyasını oku
        df = pd.read_excel('test_codes/test.xlsx')
        
        print(f"✅ Dosya başarıyla okundu!")
        print(f"📊 Boyut: {df.shape[0]} satır, {df.shape[1]} kolon")
        print(f"📝 Toplam hücre: {df.shape[0] * df.shape[1]}")
        
        print(f"\n📋 KOLON ADLARİ:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        print(f"\n📝 İLK 5 SATIR ÖRNEĞİ:")
        print(df.head().to_string())
        
        print(f"\n🔍 VERİ TİPLERİ:")
        print(df.dtypes.to_string())
        
        print(f"\n📊 BOŞ DEĞER ANALİZİ:")
        null_counts = df.isnull().sum()
        for col, count in null_counts.items():
            if count > 0:
                print(f"   {col}: {count} boş değer")
        
        # Her kolondan örnek değerler
        print(f"\n📝 HER KOLONDAN ÖRNEK DEĞERLER:")
        for col in df.columns:
            sample_values = df[col].dropna().head(3).tolist()
            print(f"   {col}: {sample_values}")
        
        return df
        
    except Exception as e:
        print(f"❌ Dosya okuma hatası: {e}")
        return None

def test_with_real_excel():
    """Gerçek Excel dosyası ile 65 issue testi"""
    print("\n🧪 GERÇEK EXCEL DOSYASI İLE 65 ISSUE TESTİ")
    print("=" * 60)
    
    # 1. Dosyayı analiz et
    df = analyze_test_excel()
    if df is None:
        return False
    
    # 2. Provider kontrolü
    print(f"\n⚙️ SİSTEM KONTROLÜ:")
    print(f"   🔧 MAX_WORKERS: {MAX_WORKERS}")
    
    try:
        from component_cards import ProviderPool
        active_providers = ProviderPool.get_active_providers()
        print(f"   🔗 Aktif provider'lar: {len(active_providers)}")
        for provider in active_providers:
            print(f"      - {provider['name']} ({provider['type']})")
    except Exception as e:
        print(f"   ❌ Provider kontrolü hatası: {e}")
        return False
    
    # 3. ExcelDataProcessor oluştur
    try:
        processor = ExcelDataProcessor(max_workers=MAX_WORKERS)
        print(f"   ✅ ExcelDataProcessor oluşturuldu ({MAX_WORKERS} worker)")
    except Exception as e:
        print(f"   ❌ ExcelDataProcessor hatası: {e}")
        return False
    
    # 4. Dosyayı işle
    input_path = 'test_codes/test.xlsx'
    output_path = 'test_codes/test_fixed.xlsx'
    log_path = 'test_codes/test_processing_log.xlsx'
    
    print(f"\n🚀 EXCEL DOSYASI İŞLENİYOR...")
    print(f"   📁 Girdi: {input_path}")
    print(f"   📁 Çıktı: {output_path}")
    print(f"   📁 Log: {log_path}")
    print(f"   🔥 {MAX_WORKERS} paralel worker ile işleniyor...")
    
    start_time = time.time()
    
    try:
        # Ana işleme fonksiyonu
        summary = processor.process_excel_file(input_path, output_path, log_path, use_streamlit=False)
        
        processing_time = time.time() - start_time
        
        print(f"\n✅ İŞLEME TAMAMLANDI! ({processing_time:.2f} saniye)")
        
        # 5. Detaylı sonuç analizi
        print(f"\n📊 DETAYLI SONUÇ ANALİZİ:")
        print(f"   📝 Toplam hücre: {summary['total_cells']:,}")
        print(f"   📊 Toplam satır: {summary['total_rows']:,}")
        print(f"   📊 Toplam kolon: {summary['total_columns']}")
        print(f"   🚨 Bulunan issue: {summary['issues_found']:,}")
        print(f"   🔧 Düzeltilen issue: {summary['issues_fixed']:,}")
        print(f"   ⏱️ İşleme süresi: {summary['processing_time_seconds']:.2f} saniye")
        print(f"   ⚡ Hücre/saniye: {summary['cells_per_second']:,.1f}")
        print(f"   🔥 Paralel worker: {MAX_WORKERS} process")
        print(f"   💻 CPU kullanımı: ~{MAX_WORKERS}x hızlanma")
        
        # 6. Log dosyası analizi
        if os.path.exists(log_path):
            print(f"\n📋 LOG DOSYASI ANALİZİ:")
            log_df = pd.read_excel(log_path)
            print(f"   📝 Toplam düzeltme kaydı: {len(log_df):,}")
            
            # Issue bazında analiz
            if len(log_df) > 0:
                issue_counts = log_df['issue_id'].value_counts().head(10)
                print(f"   🔝 En çok bulunan issue'lar:")
                for issue_id, count in issue_counts.items():
                    print(f"      Issue {issue_id}: {count:,} adet")
                
                # Kolon bazında analiz
                if 'column' in log_df.columns:
                    column_counts = log_df['column'].value_counts().head(10)
                    print(f"   📊 En çok sorunlu kolonlar:")
                    for column, count in column_counts.items():
                        print(f"      {column}: {count:,} sorun")
        
        # 7. Dosya karşılaştırması
        if os.path.exists(output_path):
            print(f"\n🔍 DOSYA KARŞILAŞTIRMASI:")
            fixed_df = pd.read_excel(output_path)
            print(f"   📊 Orijinal: {df.shape[0]:,} satır × {df.shape[1]} kolon")
            print(f"   📊 Düzeltilmiş: {fixed_df.shape[0]:,} satır × {fixed_df.shape[1]} kolon")
            
            # Örnek değişiklikler
            print(f"\n📝 ÖRNEK DEĞİŞİKLİKLER (İlk 5 kolon):")
            changes_found = False
            for col in df.columns[:5]:
                if col in fixed_df.columns:
                    for i in range(min(3, len(df))):  # İlk 3 satır
                        orig_val = str(df[col].iloc[i]) if pd.notna(df[col].iloc[i]) else "NaN"
                        fixed_val = str(fixed_df[col].iloc[i]) if pd.notna(fixed_df[col].iloc[i]) else "NaN"
                        if orig_val != fixed_val:
                            print(f"   {col}[{i}]: '{orig_val}' → '{fixed_val}'")
                            changes_found = True
            
            if not changes_found:
                print(f"   ℹ️ İlk 5 kolonun ilk 3 satırında görünür değişiklik yok")
                print(f"   💡 Değişiklikler diğer satırlarda veya log dosyasında görülebilir")
        
        # 8. Başarı oranı hesaplama
        if summary['issues_found'] > 0:
            success_rate = (summary['issues_fixed'] / summary['issues_found']) * 100
            print(f"\n🎯 BAŞARI ORANI:")
            print(f"   📈 Düzeltme başarısı: {success_rate:.1f}%")
            print(f"   🎯 Hedef: %100 başarı")
        
        return True
        
    except Exception as e:
        print(f"❌ İşleme hatası: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_with_real_excel()
    
    if success:
        print(f"\n🎉 GERÇEK EXCEL TESTİ BAŞARILI!")
        print(f"✅ test.xlsx dosyası başarıyla işlendi")
        print(f"✅ 65 issue tespit ve düzeltme sistemi çalışıyor")
        print(f"✅ {MAX_WORKERS} paralel worker maksimum performans")
        print(f"✅ Provider havuzu load balancing aktif")
        print(f"✅ Detaylı log ve sonuç dosyaları oluşturuldu")
    else:
        print(f"\n❌ GERÇEK EXCEL TESTİ BAŞARISIZ!")
        print(f"❌ test.xlsx dosyası işlenemedi")
        print(f"❌ Sistem kontrollerini yapın")
