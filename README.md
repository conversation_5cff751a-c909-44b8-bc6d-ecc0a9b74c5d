# 🔧 LLM Tabanlı Veri Ön İşleme Uygulaması

Von.md dosyasındaki **65 veri kalitesi problemini** **Qwen3:8B** modeli ile otomatik olarak tespit eden ve düzelten kapsamlı LLM uygulaması.

## 🎯 Özellikler

- **65 Veri Kalitesi Problemi**: Von.md'den alınan gerçek dünya problemleri
- **Qwen3:8B Model**: 2025'in en güncel 8B parametreli modeli
- **Paralel İşleme**: Hızlı performans için çoklu thread desteği
- **Progress Bar**: Gerçek zamanlı işleme takibi
- **Detaylı Loglama**: Tüm değişikliklerin kaydı
- **Streamlit Arayüzü**: Kullanıcı dostu web arayüzü
- **Ollama Entegrasyonu**: Lokal LLM desteği

## 🚀 Kurulum

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON>

```bash
pip install -r requirements.txt
```

### 2. <PERSON>lla<PERSON>

```bash
# Ollama'yı kurun
curl -fsSL https://ollama.ai/install.sh | sh

# Qwen3:8B modelini indirin
ollama pull qwen3:8b_ctx_32K
```

### 3. Konfigürasyon

`config.py` dosyasını düzenleyin:

```python
OLLAMA_URL="http://*************:11434"
OLLAMA_MODEL="qwen3:8b_ctx_32K"
```

## 📋 Desteklenen 65 Problem

### Para Birimi ve Sayısal Veriler
1. Para birimi normalizasyonu (TL, USD, EUR karışık)
2. Ondalık işaret normalizasyonu (nokta/virgül)
3. Sayı-metin karışık veri ayrıştırma
4. Miktar birim normalizasyonu

### Tarih ve Zaman
5. Tarih format standardizasyonu (DD-MM-YYYY vs YYYY/MM/DD)
6. Zaman dilimi normalizasyonu
7. Sipariş tarih format normalizasyonu

### İletişim Bilgileri
8. Telefon format normalizasyonu
9. E-posta format standardizasyonu
10. Adres format standardizasyonu

### Metin ve Dil
11. Dil standardizasyonu (İngilizce/Türkçe karışık)
12. Yazım hatası düzeltme
13. Boşluk sorun düzeltme (trim)

### Kategorik Veriler
14. Ürün kategori normalizasyonu
15. Müşteri kategori normalizasyonu
16. Lokasyon standardizasyonu (TR vs Turkey)

### B2B Spesifik Problemler
17. Firma isim normalizasyonu
18. Vergi dahil/hariç fiyat normalizasyonu
19. Kredi riski derecelendirme normalizasyonu
20. Fatura tip normalizasyonu

### Veri Yapısı Sorunları
21. Çoklu bilgi hücre ayrıştırma
22. Yanlış hücre veri düzeltme
23. Formül hata düzeltme
24. Eksik veri gösterim normalizasyonu

**Ve 41 problem daha...** (Toplam 65 problem)

## 🖥️ Kullanım

### Streamlit Arayüzü

```bash
streamlit run component_cards.py
```

### Komut Satırı

```python
from component_cards import ExcelDataProcessor

# İşlemciyi başlat
processor = ExcelDataProcessor()

# Excel dosyasını işle
summary = processor.process_excel_file(
    input_path="input.xlsx",
    output_path="output_fixed.xlsx", 
    log_path="processing_log.xlsx"
)

print(f"İşlenen hücre: {summary['total_cells']}")
print(f"Düzeltilen sorun: {summary['issues_fixed']}")
```

### Test

```bash
python test_data_processor.py
```

## 📊 Örnek Çıktı

### İşleme Raporu
```
📊 İşleme Raporu:
Toplam satır: 1000
Toplam kolon: 15
Toplam hücre: 15000
Bulunan sorun: 342
Düzeltilen sorun: 298
İşleme süresi: 45.2 saniye
Hız: 331.9 hücre/saniye
```

### Düzeltme Örnekleri
| Orijinal | Düzeltilmiş | Problem |
|----------|-------------|---------|
| `1000 TL` | `1000\|TRY` | Para birimi |
| `25-12-2023` | `2023-12-25` | Tarih format |
| `+90 555 123 45 67` | `+905551234567` | Telefon |
| `1,000.50` | `1000.50` | Ondalık işaret |
| ` ABC Ltd. ` | `ABC Ltd.` | Boşluk |

## 🔧 Konfigürasyon

### Ollama Ayarları
- **URL**: Ollama sunucu adresi
- **Model**: Kullanılacak model adı
- **Timeout**: İstek zaman aşımı

### İşleme Ayarları
- **Max Workers**: Paralel işçi sayısı (1-8)
- **Batch Size**: Batch büyüklüğü
- **Cache**: LLM cache kullanımı

## 📁 Dosya Yapısı

```
├── component_cards.py      # Ana uygulama
├── config.py              # Konfigürasyon
├── von.md                 # 65 problem listesi
├── test_data_processor.py # Test dosyası
├── requirements.txt       # Python gereksinimleri
└── README.md             # Bu dosya
```

## 🧪 Test Verisi

Test dosyası von.md problemlerini içeren örnek veri oluşturur:

```python
test_data = {
    'Gelir': ['1000 TL', '500 USD', '750 EUR'],
    'Tarih': ['25-12-2023', '2023/12/25', '01.01.2024'],
    'Telefon': ['+90 555 123 45 67', '0555 123 45 67'],
    'Miktar': ['5 adet', '10 kg', '2.5 litre']
}
```

## 🚨 Hata Ayıklama

### Ollama Bağlantı Hatası
```bash
# Ollama servisini kontrol et
ollama list

# Model durumunu kontrol et
ollama show qwen3:8b_ctx_32K
```

### Bellek Hatası
- Paralel işçi sayısını azaltın
- Batch boyutunu küçültün
- Daha küçük dosyalarla test edin

### Performans Optimizasyonu
- SSD kullanın
- RAM'i artırın
- GPU desteği ekleyin

## 📈 Performans

### Benchmark Sonuçları
- **10K hücre**: ~30 saniye
- **100K hücre**: ~5 dakika  
- **1M hücre**: ~45 dakika

### Sistem Gereksinimleri
- **RAM**: Minimum 8GB, Önerilen 16GB
- **CPU**: 4+ core
- **Disk**: 10GB boş alan
- **Network**: Ollama sunucu erişimi

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun
3. Değişikliklerinizi commit edin
4. Pull request gönderin

## 📄 Lisans

MIT License

## 🙏 Teşekkürler

- **Qwen Team**: Qwen3 modeli için
- **Ollama**: Lokal LLM desteği için
- **Streamlit**: Web arayüzü için
