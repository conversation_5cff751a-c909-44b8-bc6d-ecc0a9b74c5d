#!/usr/bin/env python3
"""
Issue 51 Debug - Teslimat Durum Standart
Tespit: %50, Düzeltme: %50 - Yar<PERSON> çalışıyor
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def debug_issue_51():
    """Issue 51'i detaylı debug et"""
    
    print("🔍 Issue 51 Debug - Teslimat Durum Standart")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri (paralel testten)
    test_cases = ["özel durum", "special case"]
    
    # <PERSON>ha gerçekçi teslimat durum test case'leri ekle
    delivery_status_test_cases = [
        "Teslim edildi",
        "Delivered", 
        "Yolda",
        "In transit",
        "Hazırlanıyor",
        "Preparing",
        "Kargoya verildi",
        "Shipped",
        "İptal edildi",
        "Cancelled",
        "Beklemede",
        "Pending"
    ]
    
    all_test_cases = test_cases + delivery_status_test_cases
    
    print(f"📝 Test case'leri: {len(all_test_cases)}")
    
    for i, test_case in enumerate(all_test_cases, 1):
        print(f"\n🧪 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 51 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 51]
            
            print(f"   🔧 Düzeltme: {'✅' if issue_fixes else '❌'} ({len(issue_fixes)} fix)")
            print(f"   📄 Processed: '{processed_value}'")
            
            if issue_fixes:
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            
            if not issue_detected:
                print(f"   ⚠️ PROBLEM: Issue 51 tespit edilmedi!")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")

if __name__ == "__main__":
    debug_issue_51()
