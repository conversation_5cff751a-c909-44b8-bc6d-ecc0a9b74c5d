#!/usr/bin/env python3
"""
Kalan 4 Issue'yu Detaylı Debug Et
Issue 21, 23, 24, 25 için detaylı log ve analiz
"""

from component_cards import DataQualityProcessor, OllamaLLMClient
import json
import datetime
import os

def write_log(message, log_file):
    """Log mesajını hem ekrana hem dosyaya yaz"""
    print(message)
    log_file.write(message + "\n")
    log_file.flush()

def debug_issue(issue_id, test_cases, issue_name, log_file):
    """Bir issue'yu detaylı debug et"""

    header = f"\n🔍 Issue {issue_id} ({issue_name}) Debug"
    separator = "=" * 60
    write_log(header, log_file)
    write_log(separator, log_file)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü - DETAYLI LOG
            print(f"   🔍 LLM'e gönderilen prompt analizi...")
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = issue_id in detected_issues
            
            print(f"   📊 Tespit edilen tüm issue'lar: {detected_issues}")
            print(f"   🎯 Issue {issue_id} tespit edildi mi: {'✅ EVET' if issue_detected else '❌ HAYIR'}")
            
            # 2. Process_value ile tam test - DETAYLI LOG
            if issue_detected:
                print(f"   🔧 Düzeltme işlemi başlatılıyor...")
                processed_value, fixes = processor.process_value(test_case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                print(f"   📋 Tüm düzeltmeler: {len(fixes)} adet")
                print(f"   🎯 Issue {issue_id} düzeltmeleri: {len(issue_fixes)} adet")
                
                if issue_fixes:
                    print(f"   ✅ BAŞARILI: '{processed_value}'")
                    for fix in issue_fixes:
                        print(f"      🔧 {fix['original']} → {fix['fixed']}")
                    success_count += 1
                else:
                    print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                    print(f"   🔍 İşlenmiş değer: '{processed_value}'")
            else:
                print(f"   ❌ Tespit edilmediği için düzeltme yapılmadı")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
            import traceback
            print(f"   📋 Detaylı hata: {traceback.format_exc()}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue {issue_id} Debug Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    return success_rate

def main():
    """Ana debug fonksiyonu"""
    
    print("🐛 KALAN 4 ISSUE DEBUG ANALİZİ")
    print("=" * 80)
    
    # Test case'leri - Paralel test'ten alınan gerçek veriler
    test_data = {
        21: {
            "name": "Sözleşme Koşulları",
            "cases": [
                "ödeme koşulları esnektir",
                "ödeme 30 gün içerisinde yapılır", 
                "Net 30 gün",
                "Peşin ödeme",
                "esnek ödeme",
                "30 günde ödeme"
            ]
        },
        23: {
            "name": "Kampanya İndirim",
            "cases": [
                "%10 indirim",
                "500 TL indirim",
                "10 percent off",
                "500 TL off",
                "%15",
                "1000 TL"
            ]
        },
        24: {
            "name": "Ürün Kategori Standart",
            "cases": [
                "Beyaz Eşya",
                "Ev Aletleri",
                "White Goods",
                "Home Appliances",
                "Küçük Ev Aletleri",
                "Büyük Beyaz Eşya"
            ]
        },
        25: {
            "name": "Ödeme Türleri",
            "cases": [
                "Kredi Kartı",
                "Banka Transferi",
                "Credit Card",
                "Bank Transfer",
                "Nakit",
                "Cash"
            ]
        }
    }
    
    results = {}
    
    for issue_id, data in test_data.items():
        rate = debug_issue(issue_id, data["cases"], data["name"])
        results[issue_id] = rate
    
    print(f"\n🎯 GENEL DEBUG SONUÇLARI:")
    print("=" * 50)
    for issue_id, rate in results.items():
        status = "✅" if rate == 100.0 else "❌"
        print(f"   {status} Issue {issue_id}: {rate:.1f}%")
    
    total_success = sum(1 for rate in results.values() if rate == 100.0)
    print(f"\n🏆 {total_success}/4 issue %100 başarıda!")
    
    return results

if __name__ == "__main__":
    main()
