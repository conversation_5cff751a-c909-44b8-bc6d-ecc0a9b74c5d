#!/usr/bin/env python3
"""
Issue 19 (Şirket Büyüklük) LLM İyileştirme Testi
Von.md'ye uygun test case'leri ile %100 başarı hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_19_improved():
    """Issue 19'u LLM iyileştirmesi ile test et"""
    
    print("🧪 Issue 19 (Şirket Büyüklük) LLM İyileştirme Testi")
    print("=" * 60)
    print("🎯 Hedef: %100 başarı - Von.md'ye uygun test case'leri")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Von.md'ye uygun test case'leri - Şirket büyüklüğü farklı kategoriler
    test_cases = [
        "50 çalışan",                    # <PERSON>al<PERSON>ş<PERSON> sayısı
        "KOBİ",                         # <PERSON><PERSON><PERSON>
        "Büyük Firma",                  # <PERSON><PERSON><PERSON>
        "10M TL yıllık gelir",          # <PERSON><PERSON><PERSON> bazl<PERSON>
        "Küçük İşletme",                # <PERSON><PERSON><PERSON>
        "500 personel",                 # Personel sayısı
        "yıllık gelir"                  # Eksik bilgi
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_19_detected = 19 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_19_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_19_fixes = [f for f in fixes if f['issue_id'] == 19]
            
            if issue_19_detected and issue_19_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_19_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_19_detected and not issue_19_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_19_detected}, Düzeltme={len(issue_19_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 19 Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 Issue 19 %100 BAŞARI!")
        return True
    else:
        print(f"   ⚠️ Issue 19 henüz %100 değil")
        return False

if __name__ == "__main__":
    success = test_issue_19_improved()
    
    if success:
        print("\n🎯 Issue 19 %100 başarıya ulaştı!")
        print("   ➡️ Issue 21'e geçilebilir")
    else:
        print("\n⚠️ Issue 19 daha fazla iyileştirme gerekiyor")
        print("   ➡️ Düzeltme fonksiyonunu güçlendir")
