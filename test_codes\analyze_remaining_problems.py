#!/usr/bin/env python3
"""
Kalan 45 problemi analiz et ve kategorize et
"""

def analyze_remaining_problems():
    """Kalan problemleri kategorize et"""
    
    # 21-65 arası problemler
    remaining_problems = {
        # <PERSON><PERSON><PERSON>ş<PERSON> ve Koşullar (21-29)
        21: "<PERSON><PERSON>zleşme ve sipariş koşullarında metin temelli veriler",
        22: "Kredi limitleri kolonunda farklı birimlendirmeler", 
        23: "Kampanya ve indirim bilgilerinde farklı indirim türleri",
        24: "<PERSON>r<PERSON>n kategorilerinin standart olmaması",
        25: "Ödeme türlerinde farklılık",
        26: "Fatura detaylarında farklı yapılar",
        27: "Teslimat süresi kolonunda farklı birimler",
        28: "Satış temsilcisi bilgilerinde kodlama farklılıkları",
        29: "<PERSON>t<PERSON>ş hedefleri kolonunda farklı dönemler",
        
        # <PERSON>ok ve Operasyon (30-39)
        30: "Stok birimlerinin farklı olması",
        31: "Fatura tarihleri ve ödeme tarihleri arasında tutarsızlık",
        32: "Kredi riski kolonunda farklı derecelendirme sistemleri",
        33: "Pazar segmenti bilgisinde çoklu kategoriler",
        34: "Tekrar eden müşteri bilgileri",
        35: "İskonto bilgisi kolonunda farklı tipler",
        36: "Ürün yaşam döngüsü bilgilerinde farklı aşamalar",
        37: "Gönderim ücreti kolonunda farklı birimlendirmeler",
        38: "Destek sözleşme süresi kolonunda farklı süreç ifadeleri",
        39: "Hizmet kategorilerinin farklı kodlamaları",
        
        # İletişim ve Bölgesel (40-44)
        40: "Müşteri iletişim bilgilerinde farklı formatlar",
        41: "Bölgesel fiyatlandırma farklılıkları",
        42: "El yazımı hataları ve yazım yanlışları",
        43: "Yanlış hücrede veri bulunması",
        44: "Eksik standartlara göre tarih formatları",
        
        # Veri Kalitesi (45-51)
        45: "Sayılarla birlikte metin ifadeleri",
        46: "Bir hücrede birden fazla bilgi",
        47: "Sayı ve metin karışık hücreler",
        48: "Formül kullanım hataları",
        49: "Boşluk sorunları (trimlenmemiş veriler)",
        50: "Verilerin metinsel olarak girilmesi",
        51: "Yanlış kullanılan virgül ve nokta",
        
        # Sistem Entegrasyonu (52-60)
        52: "Farklı sayfa sekmeleri arasında tutarsızlıklar",
        53: "Rakam ve harf karışık veri girilmesi",
        54: "Fatura numarası formatlarının tutarsızlığı",
        55: "Vergi numarasının eksik veya hatalı girilmesi",
        56: "İndirim bilgilerinin faturada farklı şekillerde belirtilmesi",
        57: "KDV oranlarının farklı satırlarda farklılık göstermesi",
        58: "Farklı fatura tipleri",
        59: "Faturanın düzenlendiği ve ilgili müşterinin farklı şirket adlarıyla kaydedilmesi",
        60: "İptal veya iade işlemlerinin ayrı fatura numarası ile girilmesi",
        
        # Veri Formatı (61-65)
        61: "Veri formatlarının tutarsızlığı",
        62: "İsimlendirme farklılıkları (kolon ve alan adları)",
        63: "Aynı bilginin farklı yazımlarla kaydedilmesi",
        64: "Eksik verilerin farklı şekillerde gösterimi",
        65: "Dil veya karakter seti uyuşmazlıkları"
    }
    
    print("🔍 Kalan 45 Problem Analizi")
    print("=" * 60)
    
    # Kategorilere göre grupla
    categories = {
        "Sözleşme ve Koşullar (21-29)": list(range(21, 30)),
        "Stok ve Operasyon (30-39)": list(range(30, 40)),
        "İletişim ve Bölgesel (40-44)": list(range(40, 45)),
        "Veri Kalitesi (45-51)": list(range(45, 52)),
        "Sistem Entegrasyonu (52-60)": list(range(52, 61)),
        "Veri Formatı (61-65)": list(range(61, 66))
    }
    
    for category, problem_ids in categories.items():
        print(f"\n📋 {category}")
        print("-" * 50)
        for pid in problem_ids:
            print(f"   {pid:2d}. {remaining_problems[pid]}")
    
    print(f"\n📊 Toplam kalan problem sayısı: {len(remaining_problems)}")
    
    # Öncelik önerisi
    print(f"\n🎯 Önerilen İmplementasyon Sırası:")
    print("-" * 50)
    print("1. 🔥 Veri Kalitesi (45-51) - En kritik, mevcut sistemle uyumlu")
    print("2. 📋 Sözleşme ve Koşullar (21-29) - İş mantığı önemli")
    print("3. 📦 Stok ve Operasyon (30-39) - B2B operasyonları")
    print("4. 🌐 İletişim ve Bölgesel (40-44) - Kullanıcı deneyimi")
    print("5. 🔗 Sistem Entegrasyonu (52-60) - Teknik altyapı")
    print("6. 📄 Veri Formatı (61-65) - Sistem uyumluluğu")
    
    return remaining_problems, categories

if __name__ == "__main__":
    analyze_remaining_problems()
