#!/usr/bin/env python3
"""
Paralel Test V2 - Process-Based Parallelism
Her process kendi LLM client'ı ile çalışır
"""

import multiprocessing as mp
import time
from datetime import datetime
from collections import defaultdict
import json

def worker_test_issue(args):
    """Worker fonksiyonu - Her process kendi LLM client'ı"""
    issue_id, test_cases, worker_id = args
    
    # Her worker kendi import'unu yapar (process isolation)
    from component_cards import DataQualityProcessor, OllamaLLMClient
    
    print(f"🚀 Worker {worker_id} başladı - Issue {issue_id}")
    
    results = []
    start_time = time.time()
    
    try:
        # Her worker kendi LLM client'ı oluşturur - LOAD BALANCING
        llm_client = OllamaLLMClient(worker_id=worker_id)
        processor = DataQualityProcessor(llm_client)
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                # Tespit
                detected_issues = processor.detect_issues(test_case, "test_column")
                detected = issue_id in detected_issues
                
                # Düzeltme (sadece tespit edilirse)
                fixed = False
                if detected:
                    processed_value, fixes = processor.process_value(test_case, "test_column")
                    issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                    fixed = len(issue_fixes) > 0
                
                results.append({
                    'test_case': test_case,
                    'detected': detected,
                    'fixed': fixed,
                    'worker_id': worker_id
                })
                
                # Progress
                if i % 2 == 0:
                    print(f"   Worker {worker_id}: {i}/{len(test_cases)} tamamlandı")
                    
            except Exception as e:
                print(f"   ❌ Worker {worker_id} test hatası ({test_case}): {e}")
                results.append({
                    'test_case': test_case,
                    'detected': False,
                    'fixed': False,
                    'worker_id': worker_id,
                    'error': str(e)
                })
        
        execution_time = time.time() - start_time
        print(f"✅ Worker {worker_id} tamamlandı - Issue {issue_id} - {execution_time:.1f}s")
        
        return {
            'issue_id': issue_id,
            'results': results,
            'execution_time': execution_time,
            'worker_id': worker_id
        }
        
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ Worker {worker_id} kritik hata - Issue {issue_id}: {e}")
        return {
            'issue_id': issue_id,
            'results': [],
            'execution_time': execution_time,
            'worker_id': worker_id,
            'error': str(e)
        }

class ProcessBasedParallelTest:
    """Process-based paralel test sistemi"""
    
    def __init__(self, max_workers: int = 14):
        # 14 WORKER - 3 Ollama URL'i ile load balancing
        # DNS sorunu düzeltildi, şimdi 14 worker kullanabiliriz
        self.max_workers = max_workers
        self.test_results = {}
        
    def create_test_cases(self) -> dict:
        """Test case'leri oluştur - VON.MD'YE GÖRE GERÇEK PROBLEMLER"""
        return {
            1: {
                "description": "Gelir kolonunda farklı para birimleri",
                "cases": ['1000 TL', '500 USD', '750 EUR', '2000 TL', '1500 USD', '1000 EUR', '3000 TL', '800 USD']
            },
            2: {
                "description": "Tarih kolonunda farklı formatlar",
                "cases": ['25-12-2023', '2023/12/25', '01.01.2024', '15-01-2024', '2024/01/15', '31-12-2023']
            },
            3: {
                "description": "Metin verilerinde dil farklılıkları",
                "cases": ['Good quality', 'İyi kalite', 'Excellent service', 'Mükemmel hizmet', 'Customer feedback', 'Müşteri geri bildirimi']
            },
            4: {
                "description": "Lokasyon bilgisinde farklı ülke kodları",
                "cases": ['TR', 'Turkey', 'Türkiye', 'TR-34', 'Turkey/Istanbul', 'Türkiye-Ankara']
            },
            5: {
                "description": "Telefon numarasında farklı formatlar",
                "cases": ['+90 555 123 45 67', '0555 123 45 67', '5551234567', '+90-************', '(0555) 123 45 67', '90 555 123 45 67']
            },
            6: {
                "description": "Adres bilgisinde farklı standartlar",
                "cases": ['Sokak No, Şehir, Ülke', 'Şehir, Sokak Adı, Ülke', 'İstanbul', 'Ankara, Türkiye', 'Atatürk Cad. No:123, İstanbul', 'Beyoğlu/İstanbul']
            },
            7: {
                "description": "Ürün kategorisinde farklı isimlendirme",
                "cases": ['Elektronik Kart', 'Electronic Card', 'Kart Elektronik', 'Elektronik Kartı', 'E-Kart', 'Digital Card']
            },
            8: {
                "description": "Zaman dilimlerinde farklı saat dilimleri",
                "cases": ['UTC 10:00', 'GMT*****:00', 'EEST 14:00', '2024-01-01 10:00:00 UTC', '10:30:00 GMT+3', '14:00 EEST']
            },
            9: {
                "description": "Miktar kolonunda farklı birimler",
                "cases": ['10 kg', '10000 g', '10 kilogram', '10000 gram', '10 KG', '10000 G']
            },
            10: {
                "description": "Kimlik numarası format farklılıkları",
                "cases": ["12345678901", "123 456 789 01", "123-456-789-01", "123.456.789.01", "12345 678901", "123 45 678 90 1"]
            },
            11: {
                "description": "Fiyat kolonunda farklı ondalık işaretleri",
                "cases": ["1,000.50", "1000,50", "2.500,75", "3000.25", "1,500.00", "2500,50"]
            },
            12: {
                "description": "Müşteri kategorisi farklı isimlendirmeler",
                "cases": ["Toptan", "Bayi", "Distribütör", "Toptan Müşteri", "Bayi Firma", "Distribütör Şirket"]
            },
            13: {
                "description": "Sipariş tarihleri farklı zaman formatları",
                "cases": ['2024-01-15', '15-01-2024', '15/01/2024', '2024/01/15', '15.01.2024', '01-15-2024']
            },
            14: {
                "description": "Fiyat kolonunda vergi dahil ve hariç bilgiler",
                "cases": ['1000 TL KDV dahil', '500 USD vergi hariç', '750 EUR + KDV', '1000 TL (vergi dahil)', '500 USD excluding tax', '750 EUR tax included']
            },
            15: {
                "description": "Müşteri firmalara ait isimlendirme farklılıkları",
                "cases": ['ABC Ltd.', 'ABC Limited', 'ABC Ltd. Şti.', 'ABC Şirketi', 'ABC A.Ş.', 'ABC Anonim Şirketi']
            },
            16: {
                "description": "Sözleşme zaman dilimi normalizasyonu",
                "cases": ['UTC 10:00', 'GMT*****:00', 'TRT 14:00', '2024-01-01 10:00:00 UTC']
            },
            17: {
                "description": "Ürün kod standardizasyonu",
                "cases": ['PRD-001', 'PRD001', 'prd-001', 'PRODUCT-001', 'P001']
            },
            18: {
                "description": "Sipariş adet birim normalizasyonu",
                "cases": ['5 adet', '10 piece', '3 kutu', '2 palet', '1/2 adet']
            },
            19: {
                "description": "Şirket büyüklüğü farklı kategoriler",
                "cases": ["50 çalışan", "KOBİ", "Büyük Firma", "10M TL ciro", "Küçük İşletme", "500 personel", "yıllık gelir"]
            },
            20: {
                "description": "Para birimi kur normalizasyonu",
                "cases": ['1000 USD (kur)', '750 EUR + kur', '2000 TL kuru', '1000 USD (2024 kuru)', '500 USD - eski kur']
            },
            21: {
                "description": "Sözleşme ve sipariş koşullarında metin temelli veriler",
                "cases": ["ödeme koşulları esnektir", "ödeme 30 gün içerisinde yapılır", "Net 30 gün", "Peşin ödeme", "esnek ödeme", "30 günde ödeme"]
            },
            22: {
                "description": "Kredi limitleri farklı birimlendirmeler",
                "cases": ["yıllık 100000 TL", "aylık 50000 USD", "tek seferlik 25000 EUR", "100k TL/yıl", "50k USD/ay", "25k EUR tek"]
            },
            23: {
                "description": "Kampanya ve indirim farklı türler",
                "cases": ["%10 indirim", "500 TL indirim", "10 percent off", "500 TL off", "%15", "1000 TL"]
            },
            24: {
                "description": "Ürün kategorilerinin standart olmaması",
                "cases": ["Beyaz Eşya", "Ev Aletleri", "White Goods", "Home Appliances", "Beyaz Eşyalar", "Ev Gereçleri"]
            },
            25: {
                "description": "Ödeme türlerinde farklılık",
                "cases": ["Kredi Kartı", "Banka Transferi", "Açık Hesap", "Credit Card", "Bank Transfer", "Open Account"]
            },
            26: {
                "description": "Fatura detaylarında farklı yapılar",
                "cases": ["ürün bazında fatura", "hizmet bazında fatura", "product based", "service based", "ürün detayı", "hizmet detayı"]
            },
            # Yeni Issue'lar (27-65)
            27: {
                "description": "Teslimat süre birim normalizasyonu",
                "cases": ["5 gün teslimat", "3 days delivery", "2 hafta", "1 month", "yarım hafta"]
            },
            28: {
                "description": "Satış temsilci kod normalizasyonu",
                "cases": ["A. Kaya", "Ali K.", "SR-123", "REP-456", "Mehmet A."]
            },
            29: {
                "description": "Satış hedef dönem normalizasyonu",
                "cases": ["Q1 hedef", "annual target", "monthly", "çeyreklik", "yıllık hedef"]
            },
            30: {
                "description": "Stok birim normalizasyonu",
                "cases": ["100 pieces", "50 koli", "10 pallet", "25 box", "5 adet"]
            },
            31: {
                "description": "Fatura/ödeme tarih tutarsızlık",
                "cases": ["Fatura: 01.01.2024, Ödeme: 15.01.2024", "Invoice: 2024-01-01, Payment: 2024-01-15"]
            },
            32: {
                "description": "Kredi riski derecelendirme normalizasyonu",
                "cases": ["1", "Yüksek risk", "Medium", "A+", "düşük risk"]
            },
            33: {
                "description": "Pazar segment çoklu kategori normalizasyonu",
                "cases": ["Sanayi/Toptan", "Sağlık & Teknoloji", "Eğitim/Sanayi", "Teknoloji & Sağlık"]
            },
            34: {
                "description": "Tekrarlanan müşteri bilgi birleştirme",
                "cases": ["ABC Şirketi ABC Şirketi", "XYZ Ltd. XYZ Limited", "DEF A.Ş. DEF A.Ş."]
            },
            35: {
                "description": "İskonto tip normalizasyonu",
                "cases": ["10% off", "500 TL discount", "%15 indirim", "1000 TL off"]
            },
            36: {
                "description": "Ürün yaşam döngüsü aşama normalizasyonu",
                "cases": ["new product", "mature", "developing", "yeni ürün", "olgun"]
            },
            37: {
                "description": "Gönderim ücret birim normalizasyonu",
                "cases": ["50 TL shipping", "free shipping", "ücretsiz kargo", "25 USD delivery"]
            },
            38: {
                "description": "Destek sözleşme süre normalizasyonu",
                "cases": ["6 months support", "yarım yıl", "1 year support", "2 yıl destek"]
            },
            39: {
                "description": "Hizmet kategori kod normalizasyonu",
                "cases": ["technical support", "service", "teknik destek", "servis hizmeti"]
            },
            40: {
                "description": "Müşteri iletişim format normalizasyonu",
                "cases": ["<EMAIL>", "5551234567", "www.example.com", "+90-************"]
            }
        }
    
    def run_parallel_tests(self):
        """Process-based paralel testleri çalıştır"""
        print("🚀 Process-Based Paralel Test Başlıyor")
        print("=" * 60)
        print(f"⚙️ Worker sayısı: {self.max_workers}")
        print(f"💻 CPU core sayısı: {mp.cpu_count()}")
        print(f"🕐 Başlama zamanı: {datetime.now()}")
        
        start_time = time.time()
        
        # Test case'leri hazırla
        test_cases = self.create_test_cases()
        
        # Worker argümanlarını hazırla
        worker_args = []
        for issue_id, issue_data in test_cases.items():
            worker_args.append((issue_id, issue_data['cases'], f"W{issue_id}"))
        
        total_test_cases = sum(len(data['cases']) for data in test_cases.values())
        print(f"📊 Toplam {len(worker_args)} issue, {total_test_cases} test case")
        print(f"⚡ {self.max_workers} worker ile MAKSIMUM paralel işlem!")
        print(f"🔥 Issue/Worker: {len(worker_args)/self.max_workers:.1f} (Süper hızlı!)")

        # Process pool ile paralel çalıştır
        try:
            with mp.Pool(processes=self.max_workers) as pool:
                print(f"🚀 {self.max_workers} process başlatılıyor... (16 core'dan 14'ü aktif!)")
                print(f"💻 Sistem: {mp.cpu_count()} core, {self.max_workers} worker = %{(self.max_workers/mp.cpu_count())*100:.0f} CPU kullanımı")

                # Paralel çalıştır
                results = pool.map(worker_test_issue, worker_args)
                
                # Sonuçları topla
                for result in results:
                    if result and 'issue_id' in result:
                        self.test_results[result['issue_id']] = result
                
        except Exception as e:
            print(f"❌ Process pool hatası: {e}")
            return
        
        total_time = time.time() - start_time
        print(f"⏱️ Toplam paralel süre: {total_time:.2f} saniye")
        
        # Sonuçları analiz et
        self.analyze_parallel_results()
    
    def analyze_parallel_results(self):
        """Paralel test sonuçlarını analiz et"""
        print(f"\n📊 PARALEL TEST SONUÇLARI")
        print("=" * 60)
        
        total_tests = 0
        total_detected = 0
        total_fixed = 0
        perfect_issues = 0
        
        # Issue bazında analiz
        print(f"📋 Issue Bazında Sonuçlar:")
        for issue_id in sorted(self.test_results.keys()):
            result = self.test_results[issue_id]
            
            if 'error' in result:
                print(f"Issue {issue_id:2d}: ❌ HATA - {result.get('error', 'Bilinmeyen hata')}")
                continue
            
            issue_results = result['results']
            issue_total = len(issue_results)
            issue_detected = sum(1 for r in issue_results if r['detected'])
            issue_fixed = sum(1 for r in issue_results if r['fixed'])
            
            detection_rate = (issue_detected / issue_total) * 100 if issue_total > 0 else 0
            fix_rate = (issue_fixed / issue_total) * 100 if issue_total > 0 else 0
            
            status = "✅" if detection_rate == 100 and fix_rate == 100 else "❌"
            if detection_rate == 100 and fix_rate == 100:
                perfect_issues += 1
            
            print(f"Issue {issue_id:2d}: {status} Tespit: {detection_rate:5.1f}% | Düzeltme: {fix_rate:5.1f}% | "
                  f"Tests: {issue_total} | Süre: {result['execution_time']:.1f}s | Worker: {result['worker_id']}")
            
            total_tests += issue_total
            total_detected += issue_detected
            total_fixed += issue_fixed
        
        # Genel istatistikler
        overall_detection = (total_detected / total_tests) * 100 if total_tests > 0 else 0
        overall_fix = (total_fixed / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n📊 Genel İstatistikler:")
        print(f"   📝 Toplam test: {total_tests}")
        print(f"   🔍 Toplam tespit: {total_detected} ({overall_detection:.1f}%)")
        print(f"   🔧 Toplam düzeltme: {total_fixed} ({overall_fix:.1f}%)")
        print(f"   🏆 Mükemmel issue'lar: {perfect_issues}/{len(self.test_results)}")
        
        # Paralel performans
        total_execution_time = sum(r.get('execution_time', 0) for r in self.test_results.values())
        avg_time_per_test = total_execution_time / total_tests if total_tests > 0 else 0
        
        print(f"\n⚡ SÜPER Paralel Performans:")
        print(f"   Ortalama test süresi: {avg_time_per_test:.3f} saniye")
        print(f"   Toplam işlem süresi: {total_execution_time:.2f} saniye")
        print(f"   🔥 Paralel hızlanma: ~{self.max_workers}x (14 worker!)")
        print(f"   💻 CPU kullanımı: %{(self.max_workers/mp.cpu_count())*100:.0f} (16 core'dan 14'ü)")
        
        # Başarı değerlendirmesi (hafızamdaki kurala göre)
        all_perfect = perfect_issues == len(self.test_results)
        overall_perfect = overall_detection == 100 and overall_fix == 100
        
        print(f"\n🏆 Hafızamdaki Kurala Göre Değerlendirme:")
        if all_perfect and overall_perfect:
            print(f"   🎉 TÜM ISSUE'LAR %100 BAŞARI! (Paralel test başarılı)")
        else:
            print(f"   ⚠️ Bazı issue'lar %100'e çıkarılmalı")
            for issue_id, result in self.test_results.items():
                if 'error' not in result:
                    issue_results = result['results']
                    issue_total = len(issue_results)
                    issue_detected = sum(1 for r in issue_results if r['detected'])
                    issue_fixed = sum(1 for r in issue_results if r['fixed'])
                    detection_rate = (issue_detected / issue_total) * 100
                    fix_rate = (issue_fixed / issue_total) * 100
                    
                    if detection_rate != 100 or fix_rate != 100:
                        print(f"      - Issue {issue_id}: Tespit {detection_rate:.1f}%, Düzeltme {fix_rate:.1f}%")

if __name__ == "__main__":
    # Process-based paralel test çalıştır - 14 WORKER (3 Ollama URL load balancing)
    parallel_test = ProcessBasedParallelTest(max_workers=14)
    parallel_test.run_parallel_tests()
