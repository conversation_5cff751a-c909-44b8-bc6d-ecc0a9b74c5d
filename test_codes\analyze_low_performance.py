#!/usr/bin/env python3
"""
Düşük performanslı problemleri tespit et ve analiz et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def analyze_problem_performance():
    """Tüm problemlerin performansını analiz et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Kapsamlı test case'leri (comprehensive_test.py'den)
    test_cases = {
        1: ['1000 TL', '500 USD', '750 EUR', '1,500.75 TL', '2.500,50 USD', '€ 1000', '₺1500', '$500.25'],
        2: ['25-12-2023', '2023/12/25', '01.01.2024', '1 Ocak 2024', 'Jan 1, 2024', '2024-12-25T10:30:00', 'Pazartesi, 1 Ocak 2024', 'Monday, Jan 1, 2024'],
        3: ['Hello Merhab<PERSON>', 'Customer müşteri', 'Product ürün', 'Customer müşteri service hizmet', 'Product ürün quality kalite', 'Good İyi service hizmet', 'Technology teknoloji computer bilgisayar'],
        4: ['Turkey', 'Türkiye', 'İstanbul', 'İstanbul, Turkey', 'Ankara/TR', 'Turkey - İzmir', 'TR-34', 'Bursa, Türkiye'],
        5: ['+90 555 123 45 67', '0555 123 45 67', '5551234567', '+90-555-123-4567', '(0555) 123 45 67', '90 555 123 45 67'],
        6: ['Atatürk Cad. No:123', 'Merkez Mah. 1. Sok.', 'İstanbul, Kadıköy', 'Atatürk Cad. No:123 Kat:5 Daire:10 Kadıköy/İstanbul', 'Merkez Mah. Cumhuriyet Cad. No:45 Kat:2', 'Bağdat Cad. Site Plaza Blok A Daire 15'],
        7: ['Elektronik Eşya', 'Ev Aletleri', 'Beyaz Eşya', 'Elektronik Eşya ve Aksesuarları', 'Küçük Ev Aletleri', 'Büyük Beyaz Eşya', 'Ev Elektroniği'],
        8: ['UTC', 'GMT', 'EEST', '2024-01-01 10:30:00 UTC', '2024-01-01 13:30:00 +03:00'],
        9: ['5 kg', '10 g', '2 litre', '2.5 kilogram', '1500 gram', '0.5 lt', '500 ml'],
        10: ['12345678901', '123 456 789 01', '123-456-789-01', '123.456.789.01', 'TC: 12345678901'],
        11: ['1,000.50', '1000,50', '2.500', '1,500.75', '2.500,25', '3,000.00', '4.500,50'],
        12: ['Toptan Müşteri', 'Bayi Firma', 'Distribütör Şirket', 'Yetkili Bayi Müşterisi', 'Bölge Distribütörü', 'Kurumsal B2B Müşteri', 'Perakende Satış'],
        13: ['2024-01-01', '01/01/2024', '1-1-2024', 'Pazartesi, 1 Ocak 2024', 'Monday, Jan 1, 2024', '01-Jan-2024', 'Salı, 15 Şubat 2024'],
        14: ['1000 TL + KDV', '500 TL KDV Dahil', '750 TL Vergi Hariç', '1000 TL (KDV Hariç)', '500 TL (%18 KDV Dahil)', '750 TL + %18 Vergi', '2000 TL Tax Included'],
        15: ['ABC Ltd.', 'ABC Limited', 'ABC Ltd. Şti.', 'ABC LİMİTED ŞİRKETİ', 'A.B.C. Ltd. Şti.', 'ABC LTD ŞTİ'],
        16: ['UTC 10:00', 'GMT+3 13:00', 'TRT 14:00', '2024-01-01 10:00:00 UTC', '2024-01-01 13:00:00 +03:00', '10:30:00 GMT+3', '14:00 TRT'],
        17: ['P123', 'PRO123', 'Product_123', 'PROD-123-A', 'P_123_V2', 'PRODUCT_CODE_123'],
        18: ['5 kutu', '10 palet', '100 adet', '2.5 kutu', '1/2 palet', '100 adet (12\'li paket)'],
        19: ['50 çalışan', 'KOBİ firma', 'Büyük şirket', '10M TL ciro', 'Küçük İşletme (1-9 çalışan)', '500 personel', 'Orta ölçekli işletme'],
        20: ['1000 USD (kur)', '750 EUR + kur', '2000 TL kuru', '1000 USD (2024 kuru)', '750 EUR + kur farkı', '500 USD - eski kur', '1000 USD kuru 32.50']
    }
    
    print("🔍 Düşük Performanslı Problemleri Analiz Et")
    print("=" * 60)
    
    problem_performance = {}
    
    for problem_id, test_values in test_cases.items():
        print(f"\n📝 Problem {problem_id} analiz ediliyor...")
        
        total_tests = len(test_values)
        successful_detections = 0
        successful_fixes = 0
        
        for test_value in test_values:
            try:
                # 1. Tespit kontrolü
                detected_issues = processor.detect_issues(test_value, "test_column")
                if problem_id in detected_issues:
                    successful_detections += 1
                
                # 2. Düzeltme kontrolü
                processed_value, fixes = processor.process_value(test_value, "test_column")
                problem_fixes = [f for f in fixes if f['issue_id'] == problem_id]
                if problem_fixes:
                    successful_fixes += 1
                    
            except Exception as e:
                print(f"   ❌ Hata ({test_value}): {e}")
        
        detection_rate = (successful_detections / total_tests) * 100
        fix_rate = (successful_fixes / total_tests) * 100
        
        problem_performance[problem_id] = {
            'detection_rate': detection_rate,
            'fix_rate': fix_rate,
            'total_tests': total_tests,
            'successful_detections': successful_detections,
            'successful_fixes': successful_fixes
        }
        
        print(f"   🔍 Tespit: {successful_detections}/{total_tests} ({detection_rate:.1f}%)")
        print(f"   🔧 Düzeltme: {successful_fixes}/{total_tests} ({fix_rate:.1f}%)")
    
    print(f"\n" + "=" * 60)
    print(f"📊 PERFORMANS ANALİZİ")
    print("=" * 60)
    
    # Düşük performanslı problemleri tespit et
    low_performance_problems = []
    
    for problem_id, perf in problem_performance.items():
        detection_rate = perf['detection_rate']
        fix_rate = perf['fix_rate']
        
        status = "✅"
        if detection_rate < 100 or fix_rate < 100:
            status = "❌"
            low_performance_problems.append(problem_id)
        
        print(f"Problem {problem_id:2d}: {status} Tespit: {detection_rate:5.1f}% | Düzeltme: {fix_rate:5.1f}%")
    
    print(f"\n🎯 DÜŞÜK PERFORMANSLI PROBLEMLER:")
    if low_performance_problems:
        for problem_id in low_performance_problems:
            perf = problem_performance[problem_id]
            print(f"   Problem {problem_id}: Tespit {perf['detection_rate']:.1f}% | Düzeltme {perf['fix_rate']:.1f}%")
            
            # Hangi test case'leri başarısız oldu?
            print(f"   📝 Test case'leri kontrol et:")
            for test_value in test_cases[problem_id]:
                try:
                    detected_issues = processor.detect_issues(test_value, "test_column")
                    processed_value, fixes = processor.process_value(test_value, "test_column")
                    
                    detected = problem_id in detected_issues
                    fixed = any(f['issue_id'] == problem_id for f in fixes)
                    
                    if not detected or not fixed:
                        print(f"      ❌ '{test_value}' - Tespit: {'✅' if detected else '❌'} | Düzeltme: {'✅' if fixed else '❌'}")
                        
                except Exception as e:
                    print(f"      ❌ '{test_value}' - Hata: {e}")
    else:
        print("   🎉 Tüm problemler %100 performans gösteriyor!")
    
    return low_performance_problems

if __name__ == "__main__":
    low_performance = analyze_problem_performance()
    
    if low_performance:
        print(f"\n⚠️ {len(low_performance)} problem iyileştirme gerekiyor:")
        for problem_id in low_performance:
            print(f"   - Problem {problem_id}")
        print(f"\n🔧 Bu problemleri LLM kullanarak iyileştir!")
    else:
        print(f"\n🎉 Tüm problemler mükemmel performans gösteriyor!")
