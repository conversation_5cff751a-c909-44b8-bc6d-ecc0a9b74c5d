#!/usr/bin/env python3
"""
Hızlı Test: LLM thinking mode'unun kapatılıp kapatılmadığını test et
"""

from component_cards import OllamaLLMClient, DataQualityProcessor

def test_llm_speed():
    """LLM hızını test et"""
    print("🚀 LLM Hız Testi Başlıyor...")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test verileri
    test_cases = [
        "1000 TL",
        "25-12-2023", 
        "+90 555 123 45 67",
        "1,000.50",
        " ABC Ltd. "
    ]
    
    print("\n📝 Test Sonuçları:")
    for i, test_value in enumerate(test_cases, 1):
        print(f"\n{i}. Test: '{test_value}'")
        
        # Sorun tespiti
        detected_issues = processor.detect_issues(test_value, "test_column")
        print(f"   Tespit edilen sorunlar: {detected_issues}")
        
        # Düzeltme
        if detected_issues:
            fixed_value, fixes = processor.process_value(test_value, "test_column")
            print(f"   Düzeltilmiş: '{fixed_value}'")
            
            # <think> tag kontrolü
            if '<think>' in fixed_value:
                print("   ⚠️ Hala thinking mode aktif!")
            else:
                print("   ✅ Thinking mode kapatıldı")
        else:
            print("   ℹ️ Sorun tespit edilmedi")
    
    print("\n🎉 Test tamamlandı!")

if __name__ == "__main__":
    test_llm_speed()
