#!/usr/bin/env python3
"""
Tüm 65 Issue'yu <PERSON><PERSON><PERSON><PERSON> Test Et - Her Issue %100 Başarı Hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
import json

def test_single_issue_comprehensive(issue_id):
    """Tek bir issue'yu kapsamlı test et"""
    try:
        # LLM client oluştur
        llm_client = OllamaLLMClient()
        processor = DataQualityProcessor(llm_client)
        
        # Her issue için özel test case'leri
        test_cases = get_test_cases_for_issue(issue_id)
        
        success_count = 0
        total_tests = len(test_cases)
        failed_cases = []
        
        for test_case in test_cases:
            try:
                # Tespit kontrolü
                detected_issues = processor.detect_issues(test_case, "test_column")
                issue_detected = issue_id in detected_issues
                
                # Process_value ile tam test
                processed_value, fixes = processor.process_value(test_case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                # <PERSON>şarı kriterleri
                if issue_detected and issue_fixes:
                    success_count += 1
                else:
                    failed_cases.append({
                        'test_case': test_case,
                        'detected': issue_detected,
                        'fixes': len(issue_fixes),
                        'processed': processed_value
                    })
                    
            except Exception as e:
                failed_cases.append({
                    'test_case': test_case,
                    'error': str(e)
                })
        
        success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        
        return {
            'issue_id': issue_id,
            'success_rate': success_rate,
            'success_count': success_count,
            'total_tests': total_tests,
            'failed_cases': failed_cases
        }
        
    except Exception as e:
        return {
            'issue_id': issue_id,
            'success_rate': 0,
            'success_count': 0,
            'total_tests': 1,
            'error': str(e),
            'failed_cases': []
        }

def get_test_cases_for_issue(issue_id):
    """Her issue için özel test case'leri döndür"""
    
    test_cases_map = {
        1: ["100 USD", "50 EUR", "1000 TL", "25.50 $", "€15.75"],
        2: ["2023-12-25", "25/12/2023", "25.12.2023", "Dec 25, 2023", "2023/12/25"],
        3: ["Product Name", "Customer Info", "Category Electronics", "Brand Apple", "User Profile"],
        4: ["İstanbul, TR", "Turkey", "TR-34", "Ankara/Turkey", "İzmir, Türkiye"],
        5: ["+90 ************", "0************", "************", "************", "5321234567"],
        6: ["18-25", "26-35 yaş", "65+", "Genç", "Orta yaş"],
        7: ["Elektronik", "Ev Aletleri", "Bilgisayar", "Telefon", "Tablet"],
        8: ["UTC+3", "GMT+3", "Europe/Istanbul", "Turkey Time", "TRT"],
        9: ["10 adet", "5 kg", "100 pieces", "25 kutu", "3 palet"],
        10: ["12345678901", "TC12345678901", "ID-123456", "***********-01", "12345 67890 1"],
        11: ["12,34", "12.34", "12,345.67", "1.234,56", "1,234.56"],
        12: ["VIP", "Premium", "Standard", "Gold", "Silver", "Bronze"],
        13: ["Sipariş: 2023-12-25", "Order Date: 25/12/2023", "Tarih 25.12.2023", "Date: Dec 25", "2023/12/25 Order"],
        14: ["KDV Dahil", "Tax Included", "Vergi Hariç", "VAT Excluded", "Dahil KDV"],
        15: ["ABC Ltd.", "XYZ A.Ş.", "Test Company", "Firma Ltd", "Company Inc"],
        16: ["UTC+3", "GMT+3", "Sözleşme: UTC+3", "Time: GMT+3", "Zone: UTC+3"],
        17: ["PROD-001", "ABC123", "KATEGORI-001", "ITEM_123", "CODE-456"],
        18: ["10 adet", "5 kutu", "100 pieces", "25 kg", "3 ton"],
        19: ["KOBİ", "Büyük İşletme", "Startup", "SME", "Large Company"],
        20: ["1.25 USD/TL", "1,25 EUR/USD", "Kur: 1.30", "Rate: 1.25", "Exchange: 1.20"],
        21: ["Ödeme 30 gün", "Teslimat hızlı", "Garanti 1 yıl", "Net 30 days", "Payment terms"],
        22: ["100000 TL", "50k USD", "1M EUR", "Limit: 100K", "Credit: 50000"],
        23: ["%20 indirim", "100 TL indirim", "Erken ödeme", "Discount 20%", "Sale 50%"],
        24: ["elektronik eşya", "küçük ev aletleri", "Elektronik", "Home appliances", "Electronics"],
        25: ["kredi kart", "banka havalesi", "Nakit", "Credit card", "Cash payment"],
        26: ["fatura no", "invoice number", "Fatura No", "Invoice ID", "Bill number"],
        27: ["3 gün teslimat", "1 hafta", "2 weeks delivery", "Hızlı teslimat", "Express shipping"],
        28: ["A. Kaya", "Ali K.", "REP-123", "Sales Rep", "Temsilci Kod"],
        29: ["Yıllık hedef", "Q1 target", "Aylık", "Quarterly", "Annual goal"],
        30: ["100 adet", "50 kutu", "25 palet", "Stock: 200", "Inventory: 150"],
        31: ["Fatura: 01.01.2024 Ödeme: 15.01.2024", "Invoice date payment date", "Tarih tutarsızlık"],
        32: ["A+", "Excellent", "Good", "Risk: Low", "Credit: High"],
        33: ["Sanayi/Toptan", "Sağlık&Teknoloji", "Eğitim/Finans", "Multi segment", "Mixed category"],
        34: ["ABC Şirketi ABC Ltd", "XYZ Company XYZ", "Duplicate info", "Repeated data"],
        35: ["%15 indirim", "100 TL discount", "Sale 20%", "Rebate", "Special offer"],
        36: ["Yeni ürün", "Mature product", "Growing", "Life cycle", "Development stage"],
        37: ["Ücretsiz kargo", "Shipping: 25 TL", "Free delivery", "Delivery cost", "Shipping fee"],
        38: ["1 yıl destek", "Support contract", "Warranty", "Maintenance", "Service agreement"],
        39: ["Teknik destek", "Service category", "Support service", "Technical", "Customer service"],
        40: ["<EMAIL>", "phone: 123-456", "Website info", "Contact format", "Communication"],
        41: ["İstanbul fiyat", "Regional price", "Bölgesel", "City pricing", "Location price"],
        42: ["elktronik", "müsteri", "faturra", "Spelling error", "Yazım hatası"],
        43: ["Fiyat miktar kolonunda", "Wrong cell data", "Yanlış hücre", "Misplaced info"],
        44: ["01.01", "2024.01", "Incomplete date", "Eksik tarih", "Missing year"],
        45: ["ABC123 mixed", "Text123Number", "Karışık veri", "Mixed data", "Alpha123"],
        46: ["Name: John, Age: 25", "Multiple info", "Çoklu bilgi", "Combined data"],
        47: ["Text123Number", "ABC456DEF", "Mixed format", "Karışık format", "Alpha123Beta"],
        48: ["=SUM(A1:A10)", "Formula error", "Formül hatası", "Excel formula", "Calculation"],
        49: ["Product price", "Ürün fiyat", "Price info", "Cost data", "Amount"],
        50: ["Online satış", "Store channel", "Telefon", "Sales channel", "Distribution"],
        51: ["Teslim edildi", "Delivered", "Yolda", "In transit", "Delivery status"],
        52: ["Ödeme tamamlandı", "Payment completed", "Paid", "Pending payment", "Payment status"],
        53: ["ABC123", "Code456", "ITEM789", "Alphanumeric", "Mixed code"],
        54: ["INV-12345", "FAT-67890", "Invoice123", "Bill456", "Fatura789"],
        55: ["1234567890", "VKN-123456", "Tax number", "Vergi no", "ID number"],
        56: ["%10", "100 TL", "Discount format", "İndirim", "Sale price"],
        57: ["%18 KDV", "VAT 8%", "Tax rate", "Vergi oranı", "KDV oranı"],
        58: ["Proforma fatura", "Final invoice", "Cancelled", "Invoice type", "Fatura tipi"],
        59: ["ABC Ltd ABC Şirketi", "Company name", "Şirket ismi", "Business name"],
        60: ["İptal edildi", "Cancelled", "Return", "Refund", "Iade"],
        61: ["Format tutarsızlık", "Inconsistent", "Mixed format", "Different format"],
        62: ["Müşteri adı", "Customer name", "Column name", "Field name", "Kolon ismi"],
        63: ["İSTANBUL", "ankara", "Mixed case", "Yazım farklılık", "Case difference"],
        64: ["N/A", "NULL", "", "missing", "empty", "none"],
        65: ["Türkçe karakter", "Unicode", "Character set", "Encoding", "Charset"]
    }
    
    return test_cases_map.get(issue_id, [f"test_value_{issue_id}"])

def main():
    """Ana test fonksiyonu"""
    print("🧪 TÜM 65 ISSUE KAPSAMLI TEST - %100 BAŞARI HEDEFİ")
    print("=" * 80)
    
    # Tüm issue'ları test et
    all_issues = list(range(1, 66))
    
    print(f"📋 Test edilecek issue sayısı: {len(all_issues)}")
    print(f"🎯 Hedef: Her issue %100 başarı")
    print(f"⚡ Paralel test başlıyor...\n")
    
    # Paralel test
    results = []
    with ProcessPoolExecutor(max_workers=14) as executor:
        future_to_issue = {executor.submit(test_single_issue_comprehensive, issue_id): issue_id
                          for issue_id in all_issues}

        for future in as_completed(future_to_issue):
            issue_id = future_to_issue[future]
            try:
                result = future.result()
                results.append(result)

                # İlerleme göster
                success_rate = result['success_rate']
                status = "✅" if success_rate == 100.0 else "⚠️" if success_rate >= 90 else "❌"
                print(f"{status} Issue {issue_id}: {success_rate:.1f}% ({result['success_count']}/{result['total_tests']})")

            except Exception as e:
                print(f"❌ Issue {issue_id}: HATA - {e}")
                results.append({
                    'issue_id': issue_id,
                    'success_rate': 0,
                    'error': str(e)
                })
    
    # Sonuçları analiz et
    results.sort(key=lambda x: x['issue_id'])
    
    perfect_issues = [r for r in results if r.get('success_rate', 0) == 100.0]
    good_issues = [r for r in results if 90 <= r.get('success_rate', 0) < 100]
    poor_issues = [r for r in results if r.get('success_rate', 0) < 90]
    
    print(f"\n📊 GENEL SONUÇLAR:")
    print(f"   🎯 Mükemmel (%100): {len(perfect_issues)}/65")
    print(f"   ✅ İyi (%90+): {len(good_issues)}/65") 
    print(f"   ❌ Düşük (%90-): {len(poor_issues)}/65")
    
    if len(perfect_issues) == 65:
        print(f"\n🎉 MÜKEMMEL! TÜM 65 ISSUE %100 BAŞARIYA ULAŞTI!")
    else:
        print(f"\n🔧 Düzeltilmesi Gereken Issue'lar:")
        for result in poor_issues + good_issues:
            issue_id = result['issue_id']
            success_rate = result.get('success_rate', 0)
            print(f"   Issue {issue_id}: {success_rate:.1f}%")
            
            # Başarısız test case'leri göster
            failed_cases = result.get('failed_cases', [])
            if failed_cases:
                print(f"      Başarısız case'ler: {len(failed_cases)}")
                for case in failed_cases[:3]:  # İlk 3'ünü göster
                    print(f"        - '{case.get('test_case', 'N/A')}'")
    
    return results

if __name__ == "__main__":
    results = main()
