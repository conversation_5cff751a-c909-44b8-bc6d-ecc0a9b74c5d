# 📖 LLM Veri Ön İşleme Uygulaması Kullanım Kılavuzu

## 🚀 Hızlı Başlangıç

### 1. Uygulamayı Çalıştırın
```bash
python run_app.py
```

### 2. Tarayıcıda Açın
- Otomatik olarak açılmazsa: http://localhost:8501

### 3. Excel Dosyasını Yükleyin
- "Excel dosyasını seçin" butonuna tıklayın
- .xlsx, .xls veya .csv dosyası seçin

### 4. İşlemeyi Başlatın
- "🚀 Veri İşlemeyi Başlat" butonuna tıklayın
- Progress bar'ı takip edin

### 5. Sonuçları İndirin
- Düzeltilmiş dosyayı indirin
- İşleme logunu indirin

## 🔧 Detaylı Kullanım

### Sidebar Ayarları

#### 🤖 Ollama Ayarları
- **Ollama URL**: Ollama sunucu adresi
  - Varsayılan: `http://*************:11434`
  - Lokal: `http://localhost:11434`
  
- **Ollama Model**: Kullanılacak model
  - Önerilen: `qwen3:8b_ctx_32K`
  - Alternatif: `qwen3:8b`, `llama3.1:8b`

#### 🔧 İşleme Ayarları
- **Paralel İşçi Sayısı**: 1-8 arası
  - Düşük RAM: 2-3
  - Yüksek RAM: 6-8
  
- **LLM Cache**: Hızlandırma için açık tutun

#### 📊 Model Durumu
- "🔍 Model Durumunu Kontrol Et" ile test edin
- ✅ Yeşil: Model hazır
- ❌ Kırmızı: Model problemi

### Ana Ekran

#### 📁 Dosya Yükleme
1. **Desteklenen Formatlar**:
   - Excel (.xlsx, .xls)
   - CSV (.csv)
   
2. **Dosya Boyutu**:
   - Küçük dosyalar: <1MB (hızlı)
   - Orta dosyalar: 1-10MB (normal)
   - Büyük dosyalar: >10MB (yavaş)

3. **Çıktı Dosya Adları**:
   - Otomatik oluşturulur
   - Özelleştirilebilir

#### 🚀 İşleme Süreci
1. **Dosya Yükleme**: Excel/CSV okuma
2. **Veri Analizi**: 65 problem taraması
3. **LLM İşleme**: Paralel düzeltme
4. **Sonuç Kaydetme**: Çıktı dosyaları

#### 📊 İşleme Raporu
- **Toplam Satır/Kolon**: Veri boyutu
- **Bulunan Sorun**: Tespit edilen problemler
- **Düzeltilen Sorun**: Başarılı düzeltmeler
- **İşleme Süresi**: Toplam süre
- **Hız**: Hücre/saniye

## 🔍 Von.md Problemleri

### Kategori 1: Para Birimi (1, 20)
**Sorun**: `1000 TL`, `500 USD`, `750 EUR` karışık
**Çözüm**: `1000|TRY`, `500|USD`, `750|EUR`

### Kategori 2: Tarih Formatları (2, 13, 44)
**Sorun**: `25-12-2023`, `2023/12/25`, `01.01.2024`
**Çözüm**: `2023-12-25`, `2023-12-25`, `2024-01-01`

### Kategori 3: Telefon (5)
**Sorun**: `+90 555 123 45 67`, `0555 123 45 67`
**Çözüm**: `+905551234567`, `+905551234567`

### Kategori 4: Ondalık İşaretler (11, 51)
**Sorun**: `1,000.50`, `1000,50`
**Çözüm**: `1000.50`, `1000.50`

### Kategori 5: Boşluk Sorunları (49)
**Sorun**: ` ABC Ltd. `, `  Ürün Kodu  `
**Çözüm**: `ABC Ltd.`, `Ürün Kodu`

### Kategori 6: Sayı-Metin Karışık (45, 47, 50)
**Sorun**: `5 adet`, `%10 indirim`, `1000 TL`
**Çözüm**: `5|adet`, `10|%`, `1000|TL`

### Kategori 7: Çoklu Bilgi (46)
**Sorun**: `İstanbul, Ankara, İzmir`
**Çözüm**: `İstanbul;Ankara;İzmir`

## 📋 İşleme Logu

### Log Dosyası İçeriği
- **issue_id**: Problem numarası (1-65)
- **issue_description**: Problem açıklaması
- **original**: Orijinal değer
- **fixed**: Düzeltilmiş değer
- **column**: Kolon adı
- **timestamp**: İşleme zamanı

### Log Analizi
```python
import pandas as pd

# Log dosyasını yükle
log_df = pd.read_excel('processing_log.xlsx')

# En çok bulunan problemler
problem_counts = log_df['issue_id'].value_counts()
print(problem_counts.head())

# Kolon bazında analiz
column_issues = log_df.groupby('column')['issue_id'].count()
print(column_issues)
```

## ⚡ Performans Optimizasyonu

### Hız Artırma
1. **Paralel İşçi Sayısını Artırın**: 6-8
2. **SSD Kullanın**: Disk I/O hızlandırma
3. **RAM Artırın**: 16GB+ önerilen
4. **Cache Açık Tutun**: Tekrar eden veriler için

### Bellek Optimizasyonu
1. **Küçük Batch Boyutu**: RAM sınırlıysa
2. **Az Paralel İşçi**: 2-3 işçi
3. **Büyük Dosyaları Bölün**: <10MB parçalar

### Hata Çözme
1. **Ollama Bağlantı Hatası**:
   ```bash
   ollama serve
   ollama list
   ```

2. **Model Bulunamadı**:
   ```bash
   ollama pull qwen3:8b_ctx_32K
   ```

3. **Bellek Hatası**:
   - Paralel işçi sayısını azaltın
   - Dosya boyutunu küçültün

## 📊 Örnek Kullanım Senaryoları

### Senaryo 1: Müşteri Veritabanı Temizleme
**Veri**: Müşteri isimleri, telefonlar, adresler
**Problemler**: Boşluklar, telefon formatları, yazım hataları
**Sonuç**: Standart müşteri veritabanı

### Senaryo 2: Finansal Veri Normalizasyonu
**Veri**: Gelir, gider, para birimleri
**Problemler**: Karışık para birimleri, ondalık işaretler
**Sonuç**: Tutarlı finansal rapor

### Senaryo 3: Envanter Yönetimi
**Veri**: Ürün kodları, miktarlar, birimler
**Problemler**: Farklı birimler, sayı-metin karışık
**Sonuç**: Standart envanter listesi

### Senaryo 4: CRM Veri Entegrasyonu
**Veri**: Farklı sistemlerden gelen veriler
**Problemler**: Format tutarsızlıkları, duplicate veriler
**Sonuç**: Birleştirilmiş temiz CRM verisi

## 🔒 Güvenlik ve Gizlilik

### Veri Güvenliği
- **Lokal İşleme**: Veriler sunucuya gönderilmez
- **Geçici Dosyalar**: Otomatik temizlenir
- **Cache**: Sadece hash değerleri saklanır

### Gizlilik
- **LLM Lokal**: Ollama ile lokal model
- **İnternet Gereksiz**: Offline çalışabilir
- **Log Kontrolü**: İsteğe bağlı loglama

## 🆘 Sorun Giderme

### Sık Karşılaşılan Hatalar

1. **"Model erişilemez"**
   - Ollama servisini başlatın
   - Model adını kontrol edin

2. **"Dosya yüklenemez"**
   - Dosya formatını kontrol edin
   - Dosya boyutunu küçültün

3. **"İşleme çok yavaş"**
   - Paralel işçi sayısını azaltın
   - Cache'i açın

4. **"Bellek yetersiz"**
   - Büyük dosyaları bölün
   - İşçi sayısını azaltın

### Destek
- **GitHub Issues**: Hata bildirimi
- **Dokümantasyon**: README.md
- **Test**: test_data_processor.py

## 📈 Gelişmiş Özellikler

### Özel Problem Tanımlama
```python
# Yeni problem ekle
custom_issues = {
    66: "Özel problem açıklaması"
}

# İşlemciye ekle
processor.issues.ISSUES.update(custom_issues)
```

### Batch İşleme
```python
# Birden fazla dosya
files = ['file1.xlsx', 'file2.xlsx', 'file3.xlsx']

for file in files:
    processor.process_excel_file(
        input_path=file,
        output_path=f"fixed_{file}",
        log_path=f"log_{file}"
    )
```

### API Kullanımı
```python
# Programatik kullanım
from component_cards import ExcelDataProcessor

processor = ExcelDataProcessor()
summary = processor.process_excel_file(
    input_path="data.xlsx",
    output_path="clean_data.xlsx"
)

print(f"Düzeltilen: {summary['issues_fixed']}")
```
