#!/usr/bin/env python3
"""
Yeni Issue'ları Test Et (27-65)
Tüm 65 issue'nun implement edil<PERSON><PERSON><PERSON> doğrula
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_new_issues():
    """Yeni issue'ları test et"""
    
    print("🧪 Yeni Issue'ları Test Et (27-65)")
    print("=" * 70)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri - Her yeni issue için
    test_cases = {
        27: ["5 gün teslimat", "3 days delivery", "2 hafta"],
        28: ["<PERSON><PERSON>", "Ali K.", "SR-123"],
        29: ["Q1 hedef", "annual target", "monthly"],
        30: ["100 pieces", "50 koli", "10 pallet"],
        31: ["Fatura: 01.01.2024, Ödeme: 15.01.2024"],
        32: ["1", "<PERSON><PERSON>ksek risk", "Medium"],
        33: ["Sanayi/Toptan", "Sağlık & Teknoloji"],
        34: ["ABC Şirketi ABC Şirketi", "XYZ Ltd. XYZ Limited"],
        35: ["10% off", "500 TL discount"],
        36: ["new product", "mature", "developing"],
        37: ["50 TL shipping", "free shipping"],
        38: ["6 months support", "yarım yıl"],
        39: ["technical support", "service"],
        40: ["<EMAIL>", "5551234567"],
        41: ["İstanbul Fiyatı: 100 TL", "Ankara price: 90 TL"],
        43: ["Miktar kolonunda 100 TL", "Fiyat kolonunda 5 adet"],
        53: ["abc123", "A1234B"],
        54: ["12345", "inv-123"],
        55: ["123456789", "1234567890"],
        56: ["10%", "100 TL"],
        57: ["18% VAT", "KDV 8"],
        58: ["proforma", "final invoice"],
        59: ["ABC Ltd. / ABC Limited", "XYZ Şti. XYZ"],
        60: ["cancelled", "return", "void"],
        61: ["2024-01-01", "Excel format"],
        62: ["Customer Name", "Müşteriİsim"],
        63: ["İSTANBUL", "İst."],
        64: ["N/A", "NULL", ""],
        65: ["Ã¼rÃ¼n", "Ä°stanbul"]
    }
    
    results = {}
    total_tests = 0
    successful_tests = 0
    
    for issue_id, cases in test_cases.items():
        print(f"\n🔍 Issue {issue_id} Test")
        print("=" * 50)
        
        issue_success = 0
        issue_total = len(cases)
        
        for i, test_case in enumerate(cases, 1):
            print(f"\n📝 Test {i}: '{test_case}'")
            
            try:
                # 1. Tespit kontrolü
                detected_issues = processor.detect_issues(test_case, "test_column")
                issue_detected = issue_id in detected_issues
                
                print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
                
                # 2. Process_value ile tam test
                processed_value, fixes = processor.process_value(test_case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                if issue_detected:
                    issue_success += 1
                    total_tests += 1
                    successful_tests += 1
                    print(f"   ✅ BAŞARILI: '{processed_value}'")
                    if issue_fixes:
                        for fix in issue_fixes:
                            print(f"      🔧 {fix['original']} → {fix['fixed']}")
                else:
                    total_tests += 1
                    print(f"   ❌ BAŞARISIZ: Tespit edilmedi")
                    
            except Exception as e:
                total_tests += 1
                print(f"   ❌ HATA: {e}")
        
        # Issue sonuçları
        success_rate = (issue_success / issue_total) * 100
        results[issue_id] = success_rate
        
        print(f"\n📊 Issue {issue_id} Sonuçları:")
        print(f"   📝 Toplam test: {issue_total}")
        print(f"   ✅ Başarılı: {issue_success}")
        print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    print(f"\n🎯 GENEL SONUÇLAR:")
    print("=" * 40)
    
    perfect_issues = 0
    for issue_id, rate in results.items():
        status = "✅" if rate == 100.0 else "❌"
        if rate == 100.0:
            perfect_issues += 1
        print(f"   {status} Issue {issue_id}: {rate:.1f}%")
    
    overall_success = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n🏆 ÖZET:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı test: {successful_tests}")
    print(f"   📈 Genel başarı: {overall_success:.1f}%")
    print(f"   🎯 Mükemmel issue'lar: {perfect_issues}/{len(results)}")
    
    return results

if __name__ == "__main__":
    test_new_issues()
