#!/usr/bin/env python3
"""
Issue 6 (<PERSON><PERSON>) LLM İyileştirme Testi
Von.md'ye uygun test case'leri ile %100 başarı hedefi
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_6_improved():
    """Issue 6'yı LLM iyileştirmesi ile test et"""
    
    print("🧪 Issue 6 (<PERSON><PERSON>artları) LLM İyileştirme Testi")
    print("=" * 60)
    print("🎯 Hedef: %100 başarı - Von.md'ye uygun test case'leri")
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Von.md'ye uygun test case'leri - Adres farklı standartlar
    test_cases = [
        'Sokak No, Şehir, Ülke',           # Format 1
        'Şehir, Sokak Adı, Ülke',         # Format 2
        'İstanbul',                        # Sadece şehir
        'Ankara, Türkiye',                 # <PERSON><PERSON>, <PERSON><PERSON><PERSON>
        'Atatürk Cad. No:123, İstanbul',  # Detayl<PERSON> adres
        'Beyoğlu/İstanbul'                 # İlçe/Şehir
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_6_detected = 6 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_6_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_6_fixes = [f for f in fixes if f['issue_id'] == 6]
            
            if issue_6_detected and issue_6_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_6_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_6_detected and not issue_6_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_6_detected}, Düzeltme={len(issue_6_fixes)>0}")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 6 Test Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 Issue 6 %100 BAŞARI!")
        return True
    else:
        print(f"   ⚠️ Issue 6 henüz %100 değil")
        return False

if __name__ == "__main__":
    success = test_issue_6_improved()
    
    if success:
        print("\n🎯 Issue 6 %100 başarıya ulaştı!")
        print("   ➡️ Diğer issue'lara geçilebilir")
    else:
        print("\n⚠️ Issue 6 daha fazla iyileştirme gerekiyor")
        print("   ➡️ LLM prompt'larını güçlendir")
