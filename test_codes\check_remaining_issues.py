#!/usr/bin/env python3
"""
Kalan Issue'ları Kontrol Et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def check_all_issues():
    """Tüm issue'ları kontrol et"""
    
    print("🔍 Kalan Issue'ları Kontrol Et")
    print("=" * 50)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Son testte düşük performanslı olanlar
    low_performance_issues = [5, 8, 12]  # %66.7, %66.7, %0
    
    # Hiç test edilmemiş issue'lar (13-26 arası ve 27-65)
    untested_issues = list(range(13, 27)) + list(range(27, 66))
    
    print("❌ Düşük Performanslı Issue'lar:")
    for issue_id in low_performance_issues:
        print(f"   Issue {issue_id}: Düzeltilmeli")
    
    print(f"\n⚠️ Test Edilmemiş Issue'lar:")
    for issue_id in untested_issues[:20]:  # İlk 20'sini göster
        print(f"   Issue {issue_id}: Test edilmeli")
    
    print(f"\n📊 Durum Özeti:")
    print(f"   ✅ Mükemmel Issue'lar: 5 (Issue 3, 6, 21, 25, 64)")
    print(f"   ✅ İyi Issue'lar: 7 (Issue 1, 2, 4, 7, 9, 10, 11)")
    print(f"   ❌ Düşük Performanslı: {len(low_performance_issues)}")
    print(f"   ⚠️ Test Edilmemiş: {len(untested_issues)}")
    
    # Öncelik sırası
    print(f"\n🎯 Öncelik Sırası:")
    print(f"   1. Issue 12 (%0) - EN ÖNCELİKLİ")
    print(f"   2. Issue 5 (%66.7) - Telefon format")
    print(f"   3. Issue 8 (%66.7) - Zaman dilimi")
    print(f"   4. Diğer test edilmemiş issue'lar")
    
    return low_performance_issues

def test_issue_12():
    """Issue 12'yi test et - Müşteri kategori normalizasyonu"""
    
    print(f"\n🧪 Issue 12 Test - Müşteri Kategori Normalizasyonu")
    print("=" * 50)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri
    test_cases = ["VIP", "Premium", "Standard", "Gold", "Silver", "Bronze"]
    
    success_count = 0
    
    for test_case in test_cases:
        print(f"\nTest: '{test_case}'")
        
        # Tespit kontrolü
        detected_issues = processor.detect_issues(test_case, "test_column")
        issue_detected = 12 in detected_issues
        
        print(f"   Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
        
        # Process_value ile tam test
        processed_value, fixes = processor.process_value(test_case, "test_column")
        issue_fixes = [f for f in fixes if f['issue_id'] == 12]
        
        if issue_detected and issue_fixes:
            success_count += 1
            print(f"   ✅ BAŞARILI: '{processed_value}'")
        else:
            print(f"   ❌ BAŞARISIZ: '{processed_value}'")
    
    success_rate = (success_count / len(test_cases)) * 100
    print(f"\n📊 Issue 12 Sonuç: {success_rate:.1f}% ({success_count}/{len(test_cases)})")
    
    return success_rate

if __name__ == "__main__":
    low_performance = check_all_issues()
    
    # Issue 12'yi test et (en düşük performanslı)
    if 12 in low_performance:
        test_issue_12()
