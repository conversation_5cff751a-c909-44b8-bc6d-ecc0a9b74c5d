#!/usr/bin/env python3
"""
Miktar birim düzeltme debug scripti
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def debug_quantity_unit_fix():
    """Miktar birim düzeltme fonksiyonunu debug et"""
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri (comprehensive_test.py'den)
    test_cases = [
        # Kolay testler
        "5 kg",
        "10 g", 
        "2 litre",
        # Zor testler
        "2.5 kilogram",
        "1500 gram",
        "0.5 lt",
        "500 ml"
    ]
    
    print("🔍 Miktar Birim Düzeltme Debug")
    print("=" * 50)
    
    for test_value in test_cases:
        print(f"\n📝 Test: '{test_value}'")
        
        # 1. Tespit kontrolü
        detected_issues = processor.detect_issues(test_value, "test_column")
        print(f"   Tespit edilen sorunlar: {detected_issues}")
        
        # 2. Issue 9 tespit edildi mi?
        if 9 in detected_issues:
            print("   ✅ Issue 9 tespit edildi")
            
            # 3. Düzeltme fonksiyonunu direkt çağır
            try:
                fixed_result = processor.fix_quantity_unit_issues(test_value)
                print(f"   🔧 Düzeltme sonucu: '{fixed_result}'")
                
                # 4. Sonuç değişti mi?
                if fixed_result != test_value:
                    print("   ✅ Değer değişti")
                else:
                    print("   ❌ Değer değişmedi")
                    
            except Exception as e:
                print(f"   ❌ Düzeltme hatası: {e}")
                
        else:
            print("   ❌ Issue 9 tespit edilmedi")
        
        # 5. Process_value ile tam test
        try:
            processed_value, fixes = processor.process_value(test_value, "test_column")
            print(f"   🔄 Process_value sonucu: '{processed_value}'")
            print(f"   🔧 Uygulanan düzeltmeler: {len(fixes)}")
            
            # Issue 9 düzeltmesi uygulandı mı?
            issue_9_fixes = [f for f in fixes if f['issue_id'] == 9]
            if issue_9_fixes:
                print("   ✅ Issue 9 düzeltmesi uygulandı")
                for fix in issue_9_fixes:
                    print(f"      - {fix['original']} -> {fix['fixed']}")
            else:
                print("   ❌ Issue 9 düzeltmesi uygulanmadı")
                
        except Exception as e:
            print(f"   ❌ Process_value hatası: {e}")

if __name__ == "__main__":
    debug_quantity_unit_fix()
