#!/usr/bin/env python3
"""
Issue 32 Test - <PERSON><PERSON><PERSON> Riski Derecelendirme
von.md'ye göre: Kredi riski derecelendirmelerinde normalizasyon sorunları
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_32():
    """Issue 32'yi kapsamlı test et"""
    
    print("🧪 Issue 32 Test - Kredi Riski Derecelendirme")
    print("=" * 55)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # Sayısal derecelendirmeler
        "1",
        "2", 
        "3",
        "4",
        "5",
        
        # Metin tabanlı derecelendirmeler
        "düşük risk",
        "orta risk",
        "yüksek risk",
        "low risk",
        "medium risk",
        "high risk",
        
        # Kalite ifadeleri
        "excellent",
        "very good",
        "good",
        "fair",
        "poor",
        "bad",
        
        # <PERSON><PERSON><PERSON> formatlar (kontrol)
        "A",
        "B",
        "C"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 32 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 32]
            
            # Doğru formatlar için tespit edilmemesi normal
            correct_formats = ["A", "B", "C"]
            is_correct_format = test_case in correct_formats
            
            if is_correct_format and not issue_detected:
                success_count += 1
                print(f"   ✅ BAŞARILI: Doğru format tespit edilmedi (normal)")
            elif issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 32 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 32 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_32()
