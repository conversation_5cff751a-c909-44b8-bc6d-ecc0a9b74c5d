#!/usr/bin/env python3
"""
Paralel Comprehensive Test - Thread-Safe ve Hızlı
Race condition'ları ön<PERSON>en güvenli paralel test sistemi
"""

import concurrent.futures
import threading
import time
from datetime import datetime
from collections import defaultdict
from dataclasses import dataclass
from typing import Dict, List, Tuple, Any
import queue

from component_cards import DataQualityProcessor, OllamaLLMClient

@dataclass
class TestResult:
    """Thread-safe test sonucu"""
    issue_id: int
    test_case: str
    detected: bool
    fixed: bool
    original_value: str
    processed_value: str
    fixes_applied: List[Dict]
    execution_time: float
    thread_id: str

class ThreadSafeResults:
    """Thread-safe sonuç toplama"""
    def __init__(self):
        self._lock = threading.Lock()
        self._results = []
        self._stats = defaultdict(lambda: defaultdict(int))
    
    def add_result(self, result: TestResult):
        """Thread-safe sonuç ekleme"""
        with self._lock:
            self._results.append(result)
            self._stats[result.issue_id]['total'] += 1
            if result.detected:
                self._stats[result.issue_id]['detected'] += 1
            if result.fixed:
                self._stats[result.issue_id]['fixed'] += 1
    
    def get_results(self) -> Tuple[List[TestResult], Dict]:
        """Thread-safe sonuç alma"""
        with self._lock:
            return self._results.copy(), dict(self._stats)

class ParallelComprehensiveTest:
    """Paralel kapsamlı test sistemi"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.results = ThreadSafeResults()
        self.start_time = None
        
    def create_test_cases(self) -> Dict[int, Dict]:
        """Test case'leri oluştur (orijinal comprehensive_test.py'den)"""
        test_cases = {
            1: {
                "description": "Para birimi normalizasyonu",
                "easy": ['1000 TL', '500 USD', '750 EUR'],
                "hard": ['1,500.75 TL', '2.500,50 USD', '€ 1000', '₺1500', '$500.25']
            },
            2: {
                "description": "Tarih format standardizasyonu", 
                "easy": ['25-12-2023', '2023/12/25', '01.01.2024'],
                "hard": ['1 Ocak 2024', 'Jan 1, 2024', '2024-12-25T10:30:00', 'Pazartesi, 1 Ocak 2024', 'Monday, Jan 1, 2024']
            },
            3: {
                "description": "Dil standardizasyonu",
                "easy": ['Hello Merhaba', 'Customer müşteri', 'Product ürün'],
                "hard": ['Customer müşteri service hizmet', 'Product ürün quality kalite', 'Good İyi service hizmet', 'Technology teknoloji computer bilgisayar']
            },
            4: {
                "description": "Lokasyon standardizasyonu",
                "easy": ['Turkey', 'Türkiye', 'İstanbul'],
                "hard": ['İstanbul, Turkey', 'Ankara/TR', 'Turkey - İzmir', 'TR-34', 'Bursa, Türkiye']
            },
            5: {
                "description": "Telefon format normalizasyonu",
                "easy": ['+90 555 123 45 67', '0555 123 45 67', '5551234567'],
                "hard": ['+90-************', '(0555) 123 45 67', '90 555 123 45 67']
            },
            # Diğer test case'leri (4-20) kısaltılmış...
            21: {
                "description": "Sözleşme ve sipariş koşulları",
                "easy": ["Net 30 gün", "Peşin ödeme", "1 yıl garanti"],
                "hard": ["net30gün", "peşinödeme", "1yılgaranti", "iadeedilmez", "exworks", "60günvade"]
            },
            22: {
                "description": "Kredi limitleri farklı birimlendirmeler",
                "easy": ["100000 TL", "50000 USD", "25000 EUR"],
                "hard": ["50k TL", "10k USD", "1M TL", "500 bin TL", "2 milyon USD", "100,000 TL", "50.5k EUR"]
            },
            23: {
                "description": "Kampanya ve indirim türleri",
                "easy": ["20% indirim", "100 TL indirim", "Erken ödeme"],
                "hard": ["%20 off", "100 lira indirim", "erkenödeme", "toplualım kampanyası", "sezon sonu %30", "yeni müşteri 50TL"]
            },
            24: {
                "description": "Ürün kategorilerinin standart olmaması",
                "easy": ["elektronik eşya", "ev aletleri", "beyaz eşya"],
                "hard": ["küçük ev aletleri", "büyük beyaz eşya", "giyim ve tekstil", "otomobil yedek parça", "spor malzemeleri ve fitness"]
            },
            25: {
                "description": "Ödeme türlerinde farklılık",
                "easy": ["kredi kart", "banka kartı", "nakit"],
                "hard": ["elektronik transfer", "banka havalesi", "taksitli ödeme", "cash on delivery", "credit card", "wire transfer"]
            },
            26: {
                "description": "Fatura detaylarında farklı yapılar",
                "easy": ["fatura no", "fatura tarihi", "tutar"],
                "hard": ["invoice number", "invoice date", "tax rate", "total amount", "customer name", "genel toplam"]
            }
        }
        return test_cases
    
    def test_single_case(self, issue_id: int, test_case: str, thread_id: str) -> TestResult:
        """Tek test case'i çalıştır - Thread-safe"""
        start_time = time.time()
        
        try:
            # Her thread kendi processor'ını kullanır (race condition önlemi)
            llm_client = OllamaLLMClient()
            processor = DataQualityProcessor(llm_client)
            
            # Tespit
            detected_issues = processor.detect_issues(test_case, "test_column")
            detected = issue_id in detected_issues
            
            # Düzeltme
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
            fixed = len(issue_fixes) > 0
            
            execution_time = time.time() - start_time
            
            return TestResult(
                issue_id=issue_id,
                test_case=test_case,
                detected=detected,
                fixed=fixed,
                original_value=test_case,
                processed_value=processed_value,
                fixes_applied=issue_fixes,
                execution_time=execution_time,
                thread_id=thread_id
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ Thread {thread_id} - Issue {issue_id} - '{test_case}' hatası: {e}")
            
            return TestResult(
                issue_id=issue_id,
                test_case=test_case,
                detected=False,
                fixed=False,
                original_value=test_case,
                processed_value=test_case,
                fixes_applied=[],
                execution_time=execution_time,
                thread_id=thread_id
            )
    
    def test_issue_batch(self, issue_batch: List[Tuple[int, List[str]]]) -> None:
        """Issue batch'ini test et - Worker fonksiyonu"""
        thread_id = threading.current_thread().name
        
        for issue_id, test_cases in issue_batch:
            for test_case in test_cases:
                result = self.test_single_case(issue_id, test_case, thread_id)
                self.results.add_result(result)
    
    def run_parallel_tests(self) -> None:
        """Paralel testleri çalıştır"""
        print("🚀 Paralel Kapsamlı Test Başlıyor")
        print("=" * 60)
        print(f"⚙️ Worker sayısı: {self.max_workers}")
        print(f"🕐 Başlama zamanı: {datetime.now()}")
        
        self.start_time = time.time()
        
        # Test case'leri hazırla
        test_cases = self.create_test_cases()
        
        # İş yükünü worker'lara böl
        all_work = []
        for issue_id, issue_data in test_cases.items():
            all_test_cases = issue_data.get('easy', []) + issue_data.get('hard', [])
            all_work.append((issue_id, all_test_cases))
        
        # Worker'lara iş dağıt
        work_batches = [[] for _ in range(self.max_workers)]
        for i, work_item in enumerate(all_work):
            work_batches[i % self.max_workers].append(work_item)
        
        print(f"📊 Toplam {len(all_work)} issue, {sum(len(cases) for _, cases in all_work)} test case")
        print(f"🔄 {self.max_workers} worker'a dağıtılıyor...")
        
        # Paralel çalıştır
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for i, batch in enumerate(work_batches):
                if batch:  # Boş batch'leri atla
                    future = executor.submit(self.test_issue_batch, batch)
                    futures.append(future)
                    print(f"🚀 Worker {i+1} başlatıldı ({len(batch)} issue)")
            
            # Tüm worker'ların bitmesini bekle
            concurrent.futures.wait(futures)
        
        total_time = time.time() - self.start_time
        print(f"⏱️ Toplam süre: {total_time:.2f} saniye")
        
        # Sonuçları analiz et
        self.analyze_results()
    
    def analyze_results(self) -> None:
        """Thread-safe sonuç analizi"""
        results, stats = self.results.get_results()
        
        print(f"\n📊 PARALEL TEST SONUÇLARI")
        print("=" * 60)
        
        total_tests = len(results)
        total_detected = sum(1 for r in results if r.detected)
        total_fixed = sum(1 for r in results if r.fixed)
        
        print(f"📝 Toplam test: {total_tests}")
        print(f"🔍 Toplam tespit: {total_detected} ({(total_detected/total_tests)*100:.1f}%)")
        print(f"🔧 Toplam düzeltme: {total_fixed} ({(total_fixed/total_tests)*100:.1f}%)")
        
        # Issue bazında analiz
        print(f"\n📋 Issue Bazında Sonuçlar:")
        for issue_id in sorted(stats.keys()):
            issue_stats = stats[issue_id]
            total = issue_stats['total']
            detected = issue_stats['detected']
            fixed = issue_stats['fixed']
            
            detection_rate = (detected / total) * 100 if total > 0 else 0
            fix_rate = (fixed / total) * 100 if total > 0 else 0
            
            status = "✅" if detection_rate == 100 and fix_rate == 100 else "❌"
            print(f"Issue {issue_id:2d}: {status} Tespit: {detection_rate:5.1f}% | Düzeltme: {fix_rate:5.1f}% | Tests: {total}")
        
        # Performans analizi
        avg_time = sum(r.execution_time for r in results) / len(results)
        print(f"\n⚡ Performans:")
        print(f"   Ortalama test süresi: {avg_time:.3f} saniye")
        print(f"   Toplam süre: {time.time() - self.start_time:.2f} saniye")
        print(f"   Paralel hızlanma: ~{self.max_workers}x")
        
        # Race condition kontrolü
        thread_counts = defaultdict(int)
        for result in results:
            thread_counts[result.thread_id] += 1
        
        print(f"\n🔒 Thread Safety Kontrolü:")
        for thread_id, count in thread_counts.items():
            print(f"   {thread_id}: {count} test")
        
        # Başarı değerlendirmesi
        overall_success = (total_detected == total_tests) and (total_fixed == total_tests)
        print(f"\n🏆 Genel Başarı: {'✅ %100 BAŞARI!' if overall_success else '❌ İyileştirme gerekli'}")

if __name__ == "__main__":
    # Paralel test çalıştır
    parallel_test = ParallelComprehensiveTest(max_workers=4)
    parallel_test.run_parallel_tests()
