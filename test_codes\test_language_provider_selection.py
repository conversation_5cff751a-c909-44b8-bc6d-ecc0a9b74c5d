#!/usr/bin/env python3
"""
Provider Seçimi Test Sistemi
Ollama ve OpenRouter provider'larını test eder
parallel_test_all_65.py'yi referans alır ama <PERSON>
"""

import multiprocessing as mp
import time
from datetime import datetime
from collections import defaultdict
import sys
import os

# Ana dizini path'e ekle
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def worker_test_provider_pool(args):
    """Worker fonksiyonu - Provider havuzu testleri"""
    test_cases, worker_id = args

    # Her worker kendi import'unu yapar (process isolation)
    from component_cards import DataQualityProcessor, LLMClientFactory, ProviderPool

    print(f"🚀 Worker {worker_id} başladı")

    results = []
    start_time = time.time()

    try:
        # Provider havuzundan worker'a göre LLM client oluştur
        llm_client = LLMClientFactory.create_client_from_pool(int(worker_id.replace('W', '')))
        processor = DataQualityProcessor(llm_client)

        # Hangi provider seçildiğini öğren
        provider_info = ProviderPool.get_provider_for_worker(int(worker_id.replace('W', '')))
        provider_name = provider_info['name']

        print(f"🔗 Worker {worker_id} -> {provider_name}")

        processor = DataQualityProcessor(llm_client)

        for i, test_case in enumerate(test_cases, 1):
            try:
                # Tespit testi
                detected_issues = processor.detect_issues(test_case, "test_column")
                detected = len(detected_issues) > 0

                # Düzeltme testi (sadece tespit edilirse)
                fixed = False
                if detected:
                    _, fixes = processor.process_value(test_case, "test_column")
                    fixed = len(fixes) > 0

                results.append({
                    'test_case': test_case,
                    'detected': detected,
                    'fixed': fixed,
                    'detected_issues': detected_issues,
                    'provider': provider_name,
                    'worker_id': worker_id
                })

                # Progress
                if i % 5 == 0:
                    print(f"   Worker {worker_id}: {i}/{len(test_cases)} tamamlandı")

            except Exception as e:
                print(f"   ❌ Worker {worker_id} test hatası ({test_case}): {e}")
                results.append({
                    'test_case': test_case,
                    'detected': False,
                    'fixed': False,
                    'provider': provider_name,
                    'worker_id': worker_id,
                    'error': str(e)
                })

        execution_time = time.time() - start_time
        print(f"✅ Worker {worker_id} tamamlandı - {provider_name} - {execution_time:.1f}s")

        return {
            'provider': provider_name,
            'results': results,
            'execution_time': execution_time,
            'worker_id': worker_id
        }

    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ Worker {worker_id} kritik hata: {e}")
        return {
            'provider': 'UNKNOWN',
            'results': [],
            'execution_time': execution_time,
            'worker_id': worker_id,
            'error': str(e)
        }

class ProviderPoolTest:
    """Provider havuzu test sistemi"""

    def __init__(self, max_workers: int = 14):
        self.max_workers = max_workers
        self.test_results = {}

    def create_test_cases(self) -> list:
        """Test case'leri oluştur"""
        return [
            "1000 TL", "500 USD", "750 EUR",  # Para birimi
            "25-12-2023", "2023/12/25", "01.01.2024",  # Tarih
            "İyi kalite", "Mükemmel hizmet", "Excellent service",  # Dil karışımı
            "TR", "Turkey", "Türkiye",  # Ülke
            "+90 555 123 45 67", "0555 123 45 67",  # Telefon
            "İstanbul", "Ankara", "İzmir",  # Şehir
            "Elektronik Kart", "Electronic Card",  # Kategori
            "UTC 10:00", "GMT*****:00",  # Zaman dilimi
            "10 kg", "10000 g", "10 kilogram",  # Birim
            "N/A", "NULL", "", "missing"  # Eksik veri
        ]

    def run_provider_pool_tests(self):
        """Provider havuzunu test et"""
        print("🚀 PROVIDER HAVUZU TEST BAŞLIYOR")
        print("=" * 70)
        print(f"⚙️ Worker sayısı: {self.max_workers}")
        print(f"💻 CPU core sayısı: {mp.cpu_count()}")
        print(f"🕐 Başlama zamanı: {datetime.now()}")

        # Aktif provider'ları göster
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from component_cards import ProviderPool

        active_providers = ProviderPool.get_active_providers()
        print(f"🔗 Aktif provider'lar: {len(active_providers)} adet")
        for provider in active_providers:
            print(f"   - {provider['name']} ({provider['type']})")

        if not active_providers:
            print("❌ Hiç aktif provider yok! Test durduruluyor.")
            return

        start_time = time.time()

        # Test case'leri hazırla
        test_cases = self.create_test_cases()

        # Test case'leri böl (her worker'a daha az iş ver)
        chunk_size = max(1, len(test_cases) // self.max_workers)

        # Worker argümanlarını hazırla
        worker_args = []
        worker_id = 1

        # Test case'leri chunk'lara böl
        for i in range(0, len(test_cases), chunk_size):
            chunk = test_cases[i:i + chunk_size]
            if chunk:  # Boş chunk'ları atla
                worker_args.append((chunk, f"W{worker_id}"))
                worker_id += 1

        total_test_cases = len(test_cases)
        print(f"📊 Toplam {len(worker_args)} worker, {total_test_cases} test case")
        print(f"⚡ {self.max_workers} worker ile paralel işlem!")

        # Process pool ile paralel çalıştır
        # Sadece iş sayısı kadar worker kullan
        actual_workers = min(self.max_workers, len(worker_args))
        try:
            with mp.Pool(processes=actual_workers) as pool:
                print(f"🚀 {actual_workers} process başlatılıyor... (Toplam {len(worker_args)} iş)")

                # Paralel çalıştır
                results = pool.map(worker_test_provider_pool, worker_args)

                # Sonuçları topla ve birleştir
                provider_results = defaultdict(list)
                for result in results:
                    if result:
                        provider_results[result['provider']].append(result)

                # Provider bazında sonuçları birleştir
                for provider, result_list in provider_results.items():
                    combined_results = []
                    total_time = 0

                    for result in result_list:
                        combined_results.extend(result['results'])
                        total_time += result['execution_time']

                    self.test_results[provider] = {
                        'provider': provider,
                        'results': combined_results,
                        'execution_time': total_time,
                        'worker_count': len(result_list)
                    }

        except Exception as e:
            print(f"❌ Process pool hatası: {e}")
            return

        total_time = time.time() - start_time
        print(f"⏱️ Toplam paralel süre: {total_time:.2f} saniye")

        # Sonuçları analiz et
        self.analyze_results()

    def analyze_results(self):
        """Test sonuçlarını analiz et"""
        print(f"\n📊 PROVIDER TEST SONUÇLARI")
        print("=" * 70)

        # Provider bazında analiz
        provider_stats = defaultdict(lambda: {'total': 0, 'detected': 0, 'fixed': 0, 'errors': 0})

        for provider, result in self.test_results.items():
            if 'error' in result:
                print(f"{provider}: ❌ HATA - {result.get('error', 'Bilinmeyen hata')}")
                provider_stats[provider]['errors'] += 1
                continue

            test_results = result['results']
            total = len(test_results)
            detected = sum(1 for r in test_results if r['detected'])
            fixed = sum(1 for r in test_results if r['fixed'])
            errors = sum(1 for r in test_results if 'error' in r)

            detection_rate = (detected / total) * 100 if total > 0 else 0
            fix_rate = (fixed / total) * 100 if total > 0 else 0

            status = "✅" if detection_rate > 50 and fix_rate > 50 else "❌"

            worker_count = result.get('worker_count', 1)
            print(f"{provider}: {status} Tespit: {detection_rate:5.1f}% | "
                  f"Düzeltme: {fix_rate:5.1f}% | Tests: {total} | "
                  f"Workers: {worker_count} | Süre: {result['execution_time']:.1f}s")

            # İstatistikleri güncelle
            provider_stats[provider]['total'] += total
            provider_stats[provider]['detected'] += detected
            provider_stats[provider]['fixed'] += fixed
            provider_stats[provider]['errors'] += errors

        # Provider karşılaştırması
        print(f"\n📊 Provider Karşılaştırması:")
        for provider, stats in provider_stats.items():
            if stats['total'] > 0:
                detection_rate = (stats['detected'] / stats['total']) * 100
                fix_rate = (stats['fixed'] / stats['total']) * 100
                print(f"   {provider}: Tespit {detection_rate:.1f}%, Düzeltme {fix_rate:.1f}%, "
                      f"Hatalar: {stats['errors']}")

        # Öneriler
        print(f"\n💡 Öneriler:")
        print(f"   🔧 En iyi provider: Performans verilerine göre seçin")
        print(f"   ⚙️ Konfigürasyon: config.py'de LLM_PROVIDER ayarlayın")
        print(f"   🌐 Ollama URL'leri: config.py'de OLLAMA_URL_X_ACTIVE ile kontrol edin")

if __name__ == "__main__":
    # Config'den MAX_WORKERS'ı al
    import config

    # Provider havuzu testlerini çalıştır - Tam MAX_WORKERS kullan
    test_system = ProviderPoolTest(max_workers=config.MAX_WORKERS)
    test_system.run_provider_pool_tests()
