#!/usr/bin/env python3
"""
Kalan Issue'ları <PERSON>l<PERSON> Test
Issue 14, 19, 21, 23, 24, 25 için hızlı test
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_remaining_issues():
    """Kalan issue'ları hızlıca test et"""
    
    print("🧪 Kalan Issue'lar Hızlı Test")
    print("=" * 50)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri
    test_cases = {
        14: ["1000 TL + KDV", "500 TL KDV Dahil"],  # Vergi
        19: ["50 çalışan", "KOBİ"],                 # Şirket büyüklük
        21: ["ödeme koşulları esnektir", "esnek ödeme"],  # Sözleşme
        23: ["%10 indirim", "1000 TL"],             # Kampanya
        24: ["<PERSON>az Eşya", "Home Appliances"],     # <PERSON><PERSON><PERSON><PERSON> ka<PERSON>
        25: ["Credit Card", "Kredi Kartı"]         # <PERSON>deme türleri
    }
    
    results = {}
    
    for issue_id, cases in test_cases.items():
        print(f"\n🔍 Issue {issue_id} Test:")
        success_count = 0
        
        for case in cases:
            try:
                # Tespit kontrolü
                detected_issues = processor.detect_issues(case, "test_column")
                issue_detected = issue_id in detected_issues
                
                # Process_value ile tam test
                processed_value, fixes = processor.process_value(case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                if issue_detected and issue_fixes:
                    success_count += 1
                    print(f"   ✅ '{case}' - BAŞARILI")
                else:
                    print(f"   ❌ '{case}' - BAŞARISIZ (Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0})")
                    
            except Exception as e:
                print(f"   ❌ '{case}' - HATA: {e}")
        
        success_rate = (success_count / len(cases)) * 100
        results[issue_id] = success_rate
        print(f"   📊 Issue {issue_id}: {success_rate:.1f}% başarı")
    
    print(f"\n📊 ÖZET:")
    for issue_id, rate in results.items():
        status = "✅" if rate == 100.0 else "❌"
        print(f"   {status} Issue {issue_id}: {rate:.1f}%")
    
    total_success = sum(1 for rate in results.values() if rate == 100.0)
    print(f"\n🎯 {total_success}/6 issue %100 başarıda!")
    
    return results

if __name__ == "__main__":
    test_remaining_issues()
