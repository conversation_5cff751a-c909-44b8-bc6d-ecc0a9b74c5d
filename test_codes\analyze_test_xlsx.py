#!/usr/bin/env python3
"""
test.xlsx dosyasını detaylı analiz et
"""

import pandas as pd
import numpy as np
import sys
import os

# Ana dizini path'e ekle
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_test_xlsx():
    """test.xlsx dosyasını detaylı analiz et"""
    print("📊 TEST.XLSX DOSYASI DETAYLI ANALİZİ")
    print("=" * 80)
    
    try:
        # Excel dosyasını oku
        df = pd.read_excel('test.xlsx')
        
        print(f"✅ Dosya başarıyla okundu!")
        print(f"📊 Boyut: {df.shape[0]} satır, {df.shape[1]} kolon")
        print(f"📝 Toplam hücre: {df.shape[0] * df.shape[1]}")
        
        print(f"\n📋 KOLON ADLARİ VE TİPLERİ:")
        for i, col in enumerate(df.columns, 1):
            dtype = str(df[col].dtype)
            null_count = df[col].isnull().sum()
            unique_count = df[col].nunique()
            print(f"   {i:2d}. {col:<25} | Tip: {dtype:<10} | Boş: {null_count:3d} | Unique: {unique_count:3d}")
        
        print(f"\n📝 İLK 10 SATIR ÖRNEĞİ:")
        print(df.head(10).to_string(max_cols=None, max_colwidth=30))
        
        print(f"\n🔍 HER KOLONDAN ÖRNEK DEĞERLER:")
        for col in df.columns:
            print(f"\n📋 {col}:")
            # Boş olmayan değerler
            non_null_values = df[col].dropna()
            if len(non_null_values) > 0:
                sample_values = non_null_values.head(5).tolist()
                print(f"   Örnekler: {sample_values}")
                
                # Unique değer sayısı
                unique_values = non_null_values.unique()
                print(f"   Unique değer sayısı: {len(unique_values)}")
                
                # Veri tipi analizi
                if non_null_values.dtype == 'object':
                    # String değerler için pattern analizi
                    str_values = [str(v) for v in sample_values]
                    print(f"   String örnekleri: {str_values}")
                    
                    # Potansiyel problemler
                    problems = []
                    for val in str_values:
                        val_str = str(val)
                        if any(currency in val_str.upper() for currency in ['TL', 'USD', 'EUR', '₺', '$', '€']):
                            problems.append("Para birimi tespit edildi")
                        if any(date_sep in val_str for date_sep in ['-', '/', '.']):
                            if any(char.isdigit() for char in val_str):
                                problems.append("Tarih formatı olabilir")
                        if 'NULL' in val_str.upper() or 'N/A' in val_str.upper() or val_str.strip() == '':
                            problems.append("Eksik veri")
                        if any(phone_pattern in val_str for phone_pattern in ['+90', '0555', '555']):
                            problems.append("Telefon numarası")
                    
                    if problems:
                        print(f"   🚨 Potansiyel problemler: {list(set(problems))}")
            else:
                print(f"   ⚠️ Tüm değerler boş!")
        
        print(f"\n📊 BOŞ DEĞER ANALİZİ:")
        null_analysis = df.isnull().sum()
        total_cells = len(df)
        for col, null_count in null_analysis.items():
            if null_count > 0:
                percentage = (null_count / total_cells) * 100
                print(f"   {col}: {null_count}/{total_cells} ({percentage:.1f}%) boş")
        
        print(f"\n🔍 VERİ KALİTESİ PROBLEMLERİ TESPİTİ:")
        
        # Von.md'deki 65 issue'ya göre analiz
        potential_issues = {}
        
        for col in df.columns:
            col_issues = []
            non_null_values = df[col].dropna().astype(str)
            
            for val in non_null_values.head(20):  # İlk 20 değeri kontrol et
                val_str = str(val).strip()
                
                # Issue 1, 20: Para birimi
                if any(currency in val_str.upper() for currency in ['TL', 'USD', 'EUR', '₺', '$', '€']):
                    col_issues.append("Issue 1,20: Para birimi normalizasyonu")
                
                # Issue 2, 13, 44: Tarih formatları
                if any(date_sep in val_str for date_sep in ['-', '/', '.']):
                    if any(char.isdigit() for char in val_str) and len(val_str) >= 8:
                        col_issues.append("Issue 2,13,44: Tarih format standardizasyonu")
                
                # Issue 3: Dil karışıklığı
                if any(eng_word in val_str.upper() for eng_word in ['GOOD', 'EXCELLENT', 'CUSTOMER', 'PRODUCT']):
                    col_issues.append("Issue 3: İngilizce-Türkçe karışıklığı")
                
                # Issue 5: Telefon formatları
                if any(phone_pattern in val_str for phone_pattern in ['+90', '0555', '555', '(0']):
                    col_issues.append("Issue 5: Telefon format standardizasyonu")
                
                # Issue 64: Eksik veri
                if val_str.upper() in ['NULL', 'N/A', 'NA', '', 'MISSING', 'NONE']:
                    col_issues.append("Issue 64: Eksik veri standardizasyonu")
                
                # Issue 49: Boşluk sorunları
                if val_str != val_str.strip() or '  ' in val_str:
                    col_issues.append("Issue 49: Boşluk temizleme")
                
                # Issue 42: Yazım hataları (basit kontrol)
                if any(typo in val_str.upper() for typo in ['ELKTRONIK', 'MÜŞETERI', 'ÜRÜN']):
                    col_issues.append("Issue 42: Yazım hatası düzeltme")
            
            if col_issues:
                potential_issues[col] = list(set(col_issues))
        
        if potential_issues:
            print(f"   🚨 Tespit edilen potansiyel problemler:")
            for col, issues in potential_issues.items():
                print(f"      {col}:")
                for issue in issues:
                    print(f"         - {issue}")
        else:
            print(f"   ✅ Belirgin veri kalitesi problemi tespit edilmedi")
        
        print(f"\n📈 İSTATİSTİKLER:")
        print(f"   📊 Sayısal kolonlar: {len(df.select_dtypes(include=[np.number]).columns)}")
        print(f"   📝 Metin kolonları: {len(df.select_dtypes(include=['object']).columns)}")
        print(f"   📅 Tarih kolonları: {len(df.select_dtypes(include=['datetime']).columns)}")
        print(f"   🔢 Toplam unique değer: {sum(df[col].nunique() for col in df.columns)}")
        
        return df, potential_issues
        
    except Exception as e:
        print(f"❌ Dosya okuma hatası: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    df, issues = analyze_test_xlsx()
    
    if df is not None:
        print(f"\n🎯 SONUÇ:")
        print(f"✅ test.xlsx dosyası başarıyla analiz edildi")
        print(f"📊 {df.shape[0]} satır × {df.shape[1]} kolon = {df.shape[0] * df.shape[1]} hücre")
        if issues:
            print(f"🚨 {len(issues)} kolondan veri kalitesi problemi tespit edildi")
            print(f"💡 Bu problemler von.md'deki 65 issue ile çözülebilir")
        else:
            print(f"✅ Belirgin veri kalitesi problemi tespit edilmedi")
    else:
        print(f"\n❌ Analiz başarısız!")
