#!/usr/bin/env python3
"""
Issue 27 Test - Teslimat Süre Birim Normalizasyonu
von.md'ye göre: Teslimat sürelerinde birim normalizasyon sorunları
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_issue_27():
    """Issue 27'yi kapsamlı test et"""
    
    print("🧪 Issue 27 Test - Teslimat Süre Birim Normalizasyonu")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # von.md'ye göre kapsamlı test case'leri
    test_cases = [
        # Hafta -> gün dönüşümü
        "1 hafta",
        "2 hafta",
        "1 week",
        "2 weeks",
        
        # Ay -> gün dönü<PERSON>üm<PERSON>
        "1 ay",
        "1 month",
        "2 months",
        
        # Teslimat hızı ifadeleri
        "hızlı teslimat",
        "normal teslimat",
        "yavaş teslimat",
        "fast delivery",
        "normal delivery",
        "slow delivery",
        
        # Doğru formatlar (kontrol)
        "7 gün",
        "14 gün",
        "30 gün"
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 27 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 27]
            
            # Doğru formatlar için tespit edilmemesi normal
            correct_formats = ["7 gün", "14 gün", "30 gün"]
            is_correct_format = test_case in correct_formats
            
            if is_correct_format and not issue_detected:
                success_count += 1
                print(f"   ✅ BAŞARILI: Doğru format tespit edilmedi (normal)")
            elif issue_detected and issue_fixes:
                success_count += 1
                print(f"   ✅ BAŞARILI: '{processed_value}'")
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            elif issue_detected and not issue_fixes:
                print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                print(f"   📄 Processed: '{processed_value}'")
            else:
                print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                print(f"   📄 Processed: '{processed_value}'")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Sonuçları analiz et
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n📊 Issue 27 Sonuçları:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı: {success_count}")
    print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"   🎉 MÜKEMMEL! Issue 27 %100 başarıya ulaştı!")
    else:
        print(f"   🔧 Daha fazla geliştirme gerekiyor")
    
    return success_rate

if __name__ == "__main__":
    test_issue_27()
