#!/usr/bin/env python3
"""
Issue 48 Debug - Formül Kullanım Hataları
Tespit: %0, Düzeltme: %0 - <PERSON><PERSON>ıyo<PERSON>!
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def debug_issue_48():
    """Issue 48'i detaylı debug et"""
    
    print("🔍 Issue 48 Debug - Formül Kullanım Hataları")
    print("=" * 60)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri (paralel testten)
    test_cases = ["boş hücre", "empty cell"]
    
    # Daha gerçekçi formül test case'leri ekle
    formula_test_cases = [
        "=SUM(A1:A10)",
        "=VLOOKUP(B2,Sheet2!A:B,2,FALSE)", 
        "=IF(C1>100,\"High\",\"Low\")",
        "=AVERAGE(D1:D20)",
        "=COUNT(E:E)",
        "Formula error",
        "Form<PERSON>l hatası",
        "Excel formula",
        "#REF! error",
        "#VALUE! error",
        "#DIV/0! error"
    ]
    
    all_test_cases = test_cases + formula_test_cases
    
    print(f"📝 Test case'leri: {len(all_test_cases)}")
    
    for i, test_case in enumerate(all_test_cases, 1):
        print(f"\n🧪 Test {i}: '{test_case}'")
        
        try:
            # 1. Tespit kontrolü
            detected_issues = processor.detect_issues(test_case, "test_column")
            issue_detected = 48 in detected_issues
            
            print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
            
            # 2. Process_value ile tam test
            processed_value, fixes = processor.process_value(test_case, "test_column")
            issue_fixes = [f for f in fixes if f['issue_id'] == 48]
            
            print(f"   🔧 Düzeltme: {'✅' if issue_fixes else '❌'} ({len(issue_fixes)} fix)")
            print(f"   📄 Processed: '{processed_value}'")
            
            if issue_fixes:
                for fix in issue_fixes:
                    print(f"      🔧 {fix['original']} → {fix['fixed']}")
            
            if not issue_detected:
                print(f"   ⚠️ PROBLEM: Issue 48 tespit edilmedi!")
                
        except Exception as e:
            print(f"   ❌ HATA: {e}")
    
    # Issue 48 tespit kodunu kontrol et
    print(f"\n🔍 Issue 48 Tespit Kodu Analizi:")
    print(f"   - LLM tabanlı tespit kullanılıyor mu?")
    print(f"   - Regex pattern'ler yeterli mi?")
    print(f"   - Test case'leri uygun mu?")

if __name__ == "__main__":
    debug_issue_48()
