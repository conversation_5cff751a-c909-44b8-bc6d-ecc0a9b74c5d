#!/usr/bin/env python3
"""
Test Excel dosyası oluşturucu
Von.md'deki problemleri içeren gerçek bir Excel dosyası oluşturur
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def create_test_excel():
    """Test Excel dosyası oluştur"""
    
    # Rastgele veri üretimi için seed
    random.seed(42)
    np.random.seed(42)
    
    # Test verileri - von.md problemlerini içeren
    test_data = []
    
    # 100 satır test verisi oluştur
    for i in range(100):
        row = {
            # Para birimi problemleri (Issue 1, 20)
            'Fiyat': random.choice([
                '1,500.75 TL', '2.500,50 USD', '€ 1000', '₺1500', '$500.25',
                '1000 TL', '750 EUR', '500 USD', '1000 USD (2024 kuru)',
                '750 EUR + kur farkı', '500 USD - eski kur'
            ]),
            
            # Tarih format problemleri (Issue 2, 13)
            'Tarih': random.choice([
                '25-12-2023', '2023/12/25', '01.01.2024', '1 Ocak 2024',
                'Jan 1, 2024', 'Pazartesi, 1 Ocak 2024', 'Monday, Jan 1, 2024',
                '01-Jan-2024', 'Salı, 15 Şubat 2024'
            ]),
            
            # Dil standardizasyonu (Issue 3)
            'Açıklama': random.choice([
                'Hello Merhaba', 'Customer müşteri service hizmet',
                'Product ürün quality kalite', 'Technology teknoloji computer bilgisayar',
                'Good İyi service hizmet'
            ]),
            
            # Lokasyon standardizasyonu (Issue 4)
            'Lokasyon': random.choice([
                'Turkey', 'Türkiye', 'İstanbul', 'İstanbul, Turkey',
                'Ankara/TR', 'Turkey - İzmir', 'TR-34', 'Bursa, Türkiye'
            ]),
            
            # Telefon format (Issue 5)
            'Telefon': random.choice([
                '+90 555 123 45 67', '0555 123 45 67', '5551234567',
                '+90-************', '(0555) 123 45 67', '90 555 123 45 67'
            ]),
            
            # Adres format (Issue 6)
            'Adres': random.choice([
                'Atatürk Cad. No:123', 'Merkez Mah. 1. Sok.', 'İstanbul, Kadıköy',
                'Atatürk Cad. No:123 Kat:5 Daire:10 Kadıköy/İstanbul',
                'Merkez Mah. Cumhuriyet Cad. No:45 Kat:2',
                'Bağdat Cad. Site Plaza Blok A Daire 15'
            ]),
            
            # Ürün kategori (Issue 7)
            'Kategori': random.choice([
                'Elektronik Eşya', 'Ev Aletleri', 'Beyaz Eşya',
                'Elektronik Eşya ve Aksesuarları', 'Küçük Ev Aletleri',
                'Büyük Beyaz Eşya', 'Ev Elektroniği'
            ]),
            
            # Zaman dilimi (Issue 8, 16)
            'Zaman_Dilimi': random.choice([
                'UTC', 'GMT', 'EEST', '2024-01-01 10:30:00 UTC',
                '2024-01-01 13:30:00 +03:00', 'UTC 10:00', 'GMT+3 13:00',
                'TRT 14:00', '10:30:00 GMT+3', '14:00 TRT'
            ]),
            
            # Miktar birim (Issue 9)
            'Miktar': random.choice([
                '5 kg', '10 g', '2 litre', '2.5 kilogram',
                '1500 gram', '0.5 lt', '500 ml'
            ]),
            
            # Kimlik numarası (Issue 10)
            'TC_No': random.choice([
                '12345678901', '123 456 789 01', '123-456-789-01',
                '123.456.789.01', 'TC: 12345678901'
            ]),
            
            # Ondalık işaret (Issue 11)
            'Sayı': random.choice([
                '1,000.50', '1000,50', '2.500', '1,500.75',
                '2.500,25', '3,000.00', '4.500,50'
            ]),
            
            # Müşteri kategori (Issue 12)
            'Müşteri_Tipi': random.choice([
                'Toptan Müşteri', 'Bayi Firma', 'Distribütör Şirket',
                'Yetkili Bayi Müşterisi', 'Bölge Distribütörü',
                'Kurumsal B2B Müşteri', 'Perakende Satış'
            ]),
            
            # Vergi dahil/hariç (Issue 14)
            'Vergi_Durumu': random.choice([
                '1000 TL + KDV', '500 TL KDV Dahil', '750 TL Vergi Hariç',
                '1000 TL (KDV Hariç)', '500 TL (%18 KDV Dahil)',
                '750 TL + %18 Vergi', '2000 TL Tax Included'
            ]),
            
            # Firma isim (Issue 15)
            'Firma': random.choice([
                'ABC Ltd.', 'ABC Limited', 'ABC Ltd. Şti.',
                'ABC LİMİTED ŞİRKETİ', 'A.B.C. Ltd. Şti.', 'ABC LTD ŞTİ'
            ]),
            
            # Ürün kod (Issue 17)
            'Ürün_Kodu': random.choice([
                'P123', 'PRO123', 'Product_123', 'PROD-123-A',
                'P_123_V2', 'PRODUCT_CODE_123'
            ]),
            
            # Sipariş adet birim (Issue 18)
            'Sipariş_Adedi': random.choice([
                '5 kutu', '10 palet', '100 adet', '2.5 kutu',
                '1/2 palet', '100 adet (12\'li paket)'
            ]),
            
            # Şirket büyüklük (Issue 19)
            'Şirket_Büyüklüğü': random.choice([
                '50 çalışan', 'KOBİ firma', 'Büyük şirket',
                '10M TL ciro', 'Küçük İşletme (1-9 çalışan)',
                '500 personel', 'Orta ölçekli işletme'
            ])
        }
        
        test_data.append(row)
    
    # DataFrame oluştur
    df = pd.DataFrame(test_data)
    
    # Excel dosyasına kaydet
    excel_filename = 'test_data_quality.xlsx'
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    
    print(f"✅ Test Excel dosyası oluşturuldu: {excel_filename}")
    print(f"📊 Satır sayısı: {len(df)}")
    print(f"📋 Kolon sayısı: {len(df.columns)}")
    print(f"📝 Kolonlar: {list(df.columns)}")
    
    # İlk 5 satırı göster
    print("\n📋 İlk 5 satır:")
    print(df.head().to_string())
    
    return excel_filename

if __name__ == "__main__":
    create_test_excel()
