#!/usr/bin/env python3
"""
Tamamlanan Issue'ları Test Et
Issue 31, 42, 43, 44'ü birlikte test et
"""

from component_cards import DataQualityProcessor, OllamaLLMClient

def test_completed_issues():
    """Tamamlanan issue'ları test et"""
    
    print("🧪 Tamamlanan Issue'ları Test Et")
    print("=" * 50)
    
    # LLM client oluştur
    llm_client = OllamaLLMClient()
    processor = DataQualityProcessor(llm_client)
    
    # Test case'leri
    test_data = {
        31: {
            "name": "Fatura/Ödeme Tarih Tutarsızlık",
            "cases": ["Fatura: 01.01.2024, Ödeme: 15.01.2024", "Invoice: 2024-01-01, Payment: 2024-01-15"]
        },
        42: {
            "name": "<PERSON><PERSON><PERSON>m Hataları",
            "cases": ["Elktronik Kart", "müsteri bilgisi", "fatüra detayı"]
        },
        43: {
            "name": "Yanlış Hücre Veri",
            "cases": ["Miktar kolonunda 100 TL", "Fiyat kolonunda 5 adet"]
        },
        44: {
            "name": "Tarih Format Eksiklik",
            "cases": ["01.01", "2024.01", "tarih eksik"]
        }
    }
    
    results = {}
    total_tests = 0
    successful_tests = 0
    
    for issue_id, data in test_data.items():
        print(f"\n🔍 Issue {issue_id} ({data['name']}) Test")
        print("=" * 60)
        
        issue_success = 0
        issue_total = len(data['cases'])
        
        for i, test_case in enumerate(data['cases'], 1):
            print(f"\n📝 Test {i}: '{test_case}'")
            
            try:
                # 1. Tespit kontrolü
                detected_issues = processor.detect_issues(test_case, "test_column")
                issue_detected = issue_id in detected_issues
                
                print(f"   🔍 Tespit: {'✅' if issue_detected else '❌'} (Issues: {detected_issues})")
                
                # 2. Process_value ile tam test
                processed_value, fixes = processor.process_value(test_case, "test_column")
                issue_fixes = [f for f in fixes if f['issue_id'] == issue_id]
                
                if issue_detected and issue_fixes:
                    issue_success += 1
                    total_tests += 1
                    successful_tests += 1
                    print(f"   ✅ BAŞARILI: '{processed_value}'")
                    for fix in issue_fixes:
                        print(f"      🔧 {fix['original']} → {fix['fixed']}")
                elif issue_detected and not issue_fixes:
                    total_tests += 1
                    print(f"   ⚠️ Tespit edildi ama düzeltme uygulanmadı")
                else:
                    total_tests += 1
                    print(f"   ❌ BAŞARISIZ: Tespit={issue_detected}, Düzeltme={len(issue_fixes)>0}")
                    
            except Exception as e:
                total_tests += 1
                print(f"   ❌ HATA: {e}")
        
        # Issue sonuçları
        success_rate = (issue_success / issue_total) * 100
        results[issue_id] = success_rate
        
        print(f"\n📊 Issue {issue_id} Sonuçları:")
        print(f"   📝 Toplam test: {issue_total}")
        print(f"   ✅ Başarılı: {issue_success}")
        print(f"   📈 Başarı oranı: {success_rate:.1f}%")
    
    print(f"\n🎯 GENEL SONUÇLAR:")
    print("=" * 40)
    
    perfect_issues = 0
    for issue_id, rate in results.items():
        status = "✅" if rate == 100.0 else "❌"
        if rate == 100.0:
            perfect_issues += 1
        print(f"   {status} Issue {issue_id}: {rate:.1f}%")
    
    overall_success = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n🏆 ÖZET:")
    print(f"   📝 Toplam test: {total_tests}")
    print(f"   ✅ Başarılı test: {successful_tests}")
    print(f"   📈 Genel başarı: {overall_success:.1f}%")
    print(f"   🎯 Mükemmel issue'lar: {perfect_issues}/{len(results)}")
    
    if perfect_issues == len(results):
        print(f"\n🎉 TÜM ISSUE'LAR %100 BAŞARIDA!")
    else:
        print(f"\n🔧 {len(results) - perfect_issues} issue daha geliştirilmeli")
    
    return results

if __name__ == "__main__":
    test_completed_issues()
