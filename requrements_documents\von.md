1. **<PERSON><PERSON><PERSON> Farklı Para Birimleri:** <PERSON><PERSON><PERSON><PERSON>, gelir (income) kolonu içerisinde hem TL, hem USD hem de EUR ile ifade edilen değerler olabilir.--\> Kobiye soru sorulabilir.  
2. **<PERSON>rih Kolonunda Farklı Formatlar**: Bir tarih kolonu, bazı satırlarda "DD-MM-YYYY" (örn. 25-12-2023) formatında iken diğer satırlarda "YYYY/MM/DD" (örn. 2023/12/25) formatında olabilir.  
3. **Metin Verilerinde Dil Farklılıkları**: Müşteri yorumları veya anket cevapları gibi metin verileri içeren kolonlarda bir kısım İngilizce, bir kısım Türkçe olabilir.   
4. **Lokasyon Bilgisi Kolonunda Farklı Ülke Kodları**: <PERSON><PERSON> lokasyon kolonu, bazı satırlarda ülke kodu o<PERSON> "TR" formatında, bazı satırlarda ise tam ülke adı olarak "Turkey" şeklinde ifade edilebilir.  
5. **Telefon Numarası Kolonunda Farklı Formatlar**: Telefon numaralarının bazen "+90 555 123 45 67", bazen "0555 123 45 67" ve bazen "5551234567" gibi farklı formatlarda yer alması→ Öncelik değil  
6. **Adres Bilgisi Kolonunda Farklı Standartlar**: Adres bilgileri bir kısım satırda "Sokak No, Şehir, Ülke" formatında, diğer satırlarda "Şehir, Sokak Adı, Ülke" veya sadece "Şehir" şeklinde yer alabilir.--\>Öncelik değil  
7. **Ürün Kategorisi Kolonunda Farklı İsimlendirme**: Örneğin, bir ürün kategorisi kolonu içerisinde aynı ürünü ifade eden farklı isimlendirmeler olabilir  
8. **Zaman Dilimleri Kolonunda Farklı Saat Dilimleri**: Zaman bilgisi içeren bir kolonda, bazı satırlarda UTC, bazılarında ise yerel saat dilimi (örn. EEST) ile kaydedilmiş olabilir.  
9. **Miktar Kolonunda Farklı Birimler**: Örneğin, bir miktar kolonu bazı satırlarda "kg," bazı satırlarda "g" olarak ifade edilen ağırlık birimleri içeriyor olabilir.  
10. **Kimlik Numarası Kolonunda Format Farklılıkları**: Kimlik numaralarının bazı satırlarda "12345678901" şeklinde, bazılarında ise "123 456 789 01" gibi araya boşluk eklenmiş halde yer alması  
11. **Fiyat Kolonunda Farklı Ondalık İşaretleri**: Fiyat bilgisi bazı satırlarda ondalık işaret olarak nokta (.) ile, bazı satırlarda ise virgül (,) ile ayrılmış olabilir (örn. "1,000.50" veya "1000,50").  
12. **Müşteri Kategorisi Kolonunda Farklı İsimlendirmeler**: B2B şirketlerde müşteri türleri farklı terimlerle tanımlanabilir (örn. "Toptan," "Bayi," "Distribütör" gibi).  
13. **Sipariş Tarihleri Kolonunda Farklı Zaman Formatları**: Siparişlerin farklı bölgelerden gelmesi, aynı tarih kolonunda hem yıl-ay-gün hem de gün-ay-yıl gibi formatların bulunması  
14. **Fiyat Kolonunda Vergi Dahil ve Hariç Bilgiler**: Satış fiyatları bazen vergi dahil bazen hariç olabilir. Özellikle B2B’de vergi dahil fiyatlarla hariç fiyatların aynı kolon içerisinde bulunması  
15. **Müşteri Firmalara Ait İsimlendirme Farklılıkları**:B2B verilerinde aynı müşteri firmanın adı farklı yazım biçimlerinde yer alabilir (örn. "ABC Ltd.," "ABC Limited," "ABC Ltd. Şti.").  
16. **Sözleşme Süresi Kolonunda Farklı Zaman Dilimleri**: Sözleşme başlangıç ve bitiş tarihleri farklı saat dilimlerinde olabilir. Örneğin, bir veri UTC'de, bir başka veri yerel saat diliminde kaydedilmiş olabilir.  
17. **Ürün Kodlarının Standart Olmaması**: Aynı ürün için farklı müşteri veya departmanların farklı kodlar kullanması (örneğin, "P123," "PRO123," "Product\_123")  
18. **Sipariş Adetleri Kolonunda Farklı Birimler**: B2B’de bazı müşteriler sipariş adetlerini kutu, palet, veya tekil ürün biriminde verebilir.  
19. **Şirket Büyüklüğü Bilgisinde Farklı Kategoriler**: B2B analizlerinde müşteri şirketlerin büyüklüğü, farklı kolonlarda veya farklı metriklerle belirtilmiş olabilir (çalışan sayısı, yıllık gelir gibi). Aynı kolon içinde çalışan sayısı, gelir veya kategorik sınıflandırma (KOBİ, Büyük Firma) gibi karışık veri türleri bulunabilir.  
20. **Farklı Para Birimlerinde Fiyatlandırma ve Dönemsel Kur Farkları**:Farklı para birimlerinde fiyatlandırma B2B’de yaygındır. Aynı fiyat kolonunda USD, EUR ve yerel para birimlerinin bulunması ve kur değişimleri  
21. **Sözleşme ve Sipariş Koşullarında Metin Temelli Veriler**: Sözleşme veya siparişlerdeki özel koşullar bazen serbest metin olarak kaydedilir. Örneğin, “ödeme koşulları esnektir” veya “ödeme 30 gün içerisinde yapılır” gibi farklı ifadeler.  
22. **Kredi Limitleri Kolonunda Farklı Birimlendirmeler**: Müşteri kredi limitleri bazı satırlarda yıllık, bazı satırlarda aylık veya tek seferlik olarak belirtilmiş olabilir.   
23. **Kampanya ve İndirim Bilgilerinde Farklı İndirim Türleri**: Aynı kolon içerisinde hem yüzdelik indirimler (%10 gibi), hem sabit tutar indirimler (500 TL gibi) yer alabilir.   
24. **Ürün Kategorilerinin Standart Olmaması**: B2B’de farklı müşteriler, aynı ürün kategorisini farklı terimlerle tanımlayabilir (örneğin, "Beyaz Eşya" ve "Ev Aletleri").  
25. **Ödeme Türlerinde Farklılık**: B2B işlemlerde ödeme türleri kolonunda hem “Kredi Kartı,” hem “Banka Transferi,” hem de “Açık Hesap” gibi farklı ödeme tipleri bulunabilir.  
26. **Fatura Detaylarında Farklı Yapılar**: Fatura detayları, bazı satırlarda ürün bazında bazı satırlarda hizmet bazında düzenlenmiş olabilir.  
27. **Teslimat Süresi Kolonunda Farklı Birimler**: Teslimat süresi bazı satırlarda gün olarak belirtilirken (örneğin, "5 gün"), bazı satırlarda hafta ("1 hafta") veya ay ("1 ay") olarak ifade edilebilir.  
28. **Satış Temsilcisi Bilgilerinde Kodlama Farklılıkları**: B2B şirketlerde satış temsilcilerinin isimleri veya kodları farklı şekillerde kaydedilebilir (örneğin, "Ali K." ve "A. Kaya" aynı kişi için kullanılabilir).  
29. **Satış Hedefleri Kolonunda Farklı Dönemler**: Satış hedefleri bazı satırlarda yıllık, bazı satırlarda çeyreklik veya aylık olarak kaydedilmiş olabilir.  
30. **Stok Birimlerinin Farklı Olması**: Ürünlerin stok miktarları farklı birimlerde (örneğin, “adet,” “koli,” veya “palet”) kaydedilebilir.  
31. **Fatura Tarihleri ve Ödeme Tarihleri Arasında Tutarsızlık**: Fatura tarihleri ve ödeme tarihleri bazı durumlarda aynı kolon içinde bulunabilir. Örneğin, "Fatura Tarihi: 01-01-2024, Ödeme Tarihi: 10-01-2024" şeklinde bir satır  
32. **Kredi Riski Kolonunda Farklı Derecelendirme Sistemleri**: Kredi riskleri bazı kayıtlarda A, B, C gibi harflerle, bazı kayıtlarda 1, 2, 3 gibi sayısal değerlerle ifade edilebilir.   
33. **Pazar Segmenti Bilgisinde Çoklu Kategoriler**: Bazı müşteriler birden fazla pazarda faaliyet gösterebilir ve bu durum pazar segmenti kolonunda farklı isimlendirme veya birden fazla etiket kullanılarak belirtilmiş olabilir. Örneğin, "Sanayi/Toptan" veya "Sağlık & Teknoloji".   
34. **Tekrar Eden Müşteri Bilgileri**: Bazı müşteriler farklı kodlar veya yazımlar altında tekrar etmiş olabilir. Aynı müşterinin farklı kayıtlarda "ABC Şirketi" ve "A.B.C. Şirketi" gibi farklı isimlerle kaydedilmesİ  
35. **İskonto Bilgisi Kolonunda Farklı Tipler**: İskonto oranları hem yüzde hem de tutar olarak kaydedilmiş olabilir. Örneğin, "%10 indirim" veya "500 TL indirim" gibi ifadeler aynı kolon içinde  
36. **Ürün Yaşam Döngüsü Bilgilerinde Farklı Aşamalar**: B2B ürünlerinde yaşam döngüsü farklı aşamalarda kaydedilebilir (örneğin, “Yeni Çıkan,” “Olgun,” “Gelişmekte Olan”). Ancak bu aşamalar bazen standart olmayabilir.  
37. **Gönderim Ücreti Kolonunda Farklı Birimlendirmeler**: Gönderim ücretleri bazı satırlarda birim başına, bazı satırlarda toplu olarak kaydedilmiş olabilir.  
38. **Destek Sözleşme Süresi Kolonunda Farklı Süreç İfadeleri**: Bazı sözleşme süreleri “6 ay,” bazıları “Yarım Yıl” gibi farklı ifade şekilleriyle kaydedilebilir.  
39. **Hizmet Kategorilerinin Farklı Kodlamaları**: Özellikle B2B’de verilen hizmetlerin kategorileri farklı isimlendirmelerle kaydedilmiş olabilir. Örneğin, “Teknik Destek,” “Servis Hizmeti,” ve “Destek” aynı hizmeti ifade ediyor olabilir.  
40. **Müşteri İletişim Bilgilerinde Farklı Formatlar**: Telefon numaraları, e-posta adresleri veya adresler farklı formatlarda kaydedilmiş olabilir. Örneğin, e-posta adresleri bazen büyük harflerle bazen küçük harflerle kaydedilmiş olabilir.  
41. **Bölgesel Fiyatlandırma Farklılıkları**: Fiyat kolonunda aynı ürün için farklı bölgelerde farklı fiyatlandırmalar olabilir (örneğin, “İstanbul Fiyatı” ve “Ankara Fiyatı”).  
42. **El Yazımı Hataları ve Yazım Yanlışları**: Örneğin, aynı ürünün ismi bazen "Elektronik Kart," bazen "Elktronik Kart" gibi hatalı yazılmış olabilir.  
43. **Yanlış Hücrede Veri Bulunması**: Bazı veriler yanlış hücreye kaydedilebilir. Örneğin, fiyat bilgisi “Miktar” kolonunda veya “Sipariş Tarihi” “Teslimat Tarihi” kolonunda bulunabilir.  
44. **Eksik Standartlara Göre Tarih Formatları**: Tarihler hücrelerde farklı formatlarda girilebilir (örneğin, "01.01.2024," "1 Ocak 2024," "Jan 1, 2024"), hatta bazı hücrelerde tarih yerine "Daha Sonra Belirlenecek" gibi metin ifadeleri olabilir.  
45. **Sayılarla Birlikte Metin İfadeleri**:Bir hücrede yalnızca sayılar beklenirken, yanında "adet" veya "kg" gibi birimlerin yazılmış olması, analizde sayısal işlemleri zorlaştırır.  
46. **Bir Hücrede Birden Fazla Bilgi**: Bazı hücrelerde birden fazla bilgi bulunabilir, örneğin "İstanbul, Ankara, İzmir" gibi birden fazla şehir adı veya “5 ürün, 2 yedek parça” gibi birden fazla sayı bilgisi.  
47. **Sayı ve Metin Karışık Hücreler**: Özellikle indirim veya kampanya gibi kolonlarda sayılar ve metinler aynı hücre içinde bulunabilir. Örneğin, “%10 İndirim” veya “500 TL indirim” gibi ifadeler.  
48. **Formül Kullanım Hataları**: Bazı hücrelerde formüller kullanılmış olabilir ve bu formüller kopyalandığında yanlış sonuçlar doğurabilir. Örneğin, bir toplam hesaplama formülü yanlış hücrelere uygulanmış olabilir ve yanlış değerler verebilir.  
49. **Boşluk Sorunları (Trimlenmemiş Veriler)**: Hücrelerde metinlerin başında veya sonunda gereksiz boşluklar bulunabilir. Örneğin, “Müşteri Adı ” veya “ Ürün Kodu” gibi fazladan boşluklarla girilen veriler  
50. **Verilerin Metinsel Olarak Girilmesi**: Sayısal veriler bazen metin olarak girilebilir (örneğin, "1000 TL" veya "5.000" şeklinde).  
51. **Yanlış Kullanılan Virgül ve Nokta (Ondalık ve Binlik Ayraç)**: Ondalık ayırıcı olarak hem nokta hem virgül kullanılmış olabilir (örneğin, "1.000" yerine "1,000").  
52. **Farklı Sayfa Sekmeleri Arasında Tutarsızlıklar**: Bazı bilgiler birden fazla sayfa sekmesinde tutulabilir ve her sekmede güncellenmeyebilir.  
53. **Rakam ve Harf Karışık Veri Girilmesi**: Özellikle stok kodları veya müşteri numaraları gibi alanlarda hem harf hem rakamlar içeren düzensiz formatlar olabilir. Örneğin, “A1234” ve “1234A” gibi  
54. **Fatura Numarası Formatlarının Tutarsızlığı**: Fatura numaraları bazı kayıtlarda yalnızca sayılardan, bazı kayıtlarda harf ve rakam karışımından oluşabilir (örneğin, "12345" ve "INV-12345" gibi).  
55. **Vergi Numarasının Eksik veya Hatalı Girilmesi**: Vergi numarası bazı satırlarda eksik veya hatalı girilmiş olabilir.   
56. **İndirim Bilgilerinin Faturada Farklı Şekillerde Belirtilmesi**: Fatura üzerinde indirim oranı bazen yüzdelik (%10), bazen tutar (100 TL) olarak ifade edilebilir.  
57. **KDV Oranlarının Farklı Satırlarda Farklılık Göstermesi**: Özellikle çok kalemli faturalarda bazı ürünlerde farklı KDV oranları uygulanabilir (örneğin, %8 ve %18).   
58. **Farklı Fatura Tipleri (Proforma, Kesin Fatura, İptal Faturası)**: B2B faturalarda proforma (ön fatura), kesin fatura ve iptal edilmiş faturalar aynı veri dosyasında bulunabilir.   
59. **Faturanın Düzenlendiği ve İlgili Müşterinin Farklı Şirket Adlarıyla Kaydedilmesi**: Aynı müşteriye ait faturalar farklı isimler veya kısaltmalarla girilmiş olabilir (örn. "ABC Ltd. Şti." ve "ABC Şirketi").  
60. **İptal veya İade İşlemlerinin Ayrı Fatura Numarası ile Girilmesi**: İptal veya iade edilen işlemler, farklı bir fatura numarasıyla fakat aynı müşteri ve ürün bilgileriyle girilmiş olabilir.  
61. **Veri Formatlarının Tutarsızlığı**: Excel dosyalarında tarih formatı "DD-MM-YYYY" şeklinde olabilirken, CRM veritabanında "YYYY-MM-DD" olarak tutulmuş olabilir.  
62. **İsimlendirme Farklılıkları (Kolon ve Alan Adları)**: Aynı bilgiye karşılık gelen kolonlar Excel’de farklı, CRM’de farklı isimlendirilmiş olabilir. Örneğin, müşteri adı Excel’de “Müşteri Adı” iken CRM’de “Müşteriİsim” olarak kaydedilmiş olabilir.  
63. **Aynı Bilginin Farklı Yazımlarla Kaydedilmesi**: Excel’de manuel olarak girilen verilerde yazım hataları veya farklılıkları olabilir (örneğin, “İstanbul” yerine “İst.” veya “IST”). CRM veritabanında ise yazım standartları kullanılmış olabilir.  
64. **Eksik Verilerin Farklı Şekillerde Gösterimi**: CRM’de eksik veriler NULL olarak tutulurken, Excel’de boş hücreler veya “N/A” gibi ifadeler bulunabilir.   
65. **Dil veya Karakter Seti Uyuşmazlıkları**: CRM veritabanında Unicode karakter desteği bulunurken, Excel dosyasında Türkçe karakterler düzgün görünmeyebilir

**HANDLE DUPLICATED DATA**

- Tüm firma isimlerinin büyük harfe veya küçük harfe(önerilir) çevrilmesi karşılaştırmayı kolaylaştıracaktır.  
- Firma isimlerindeki kısaltmaların normalize edilmesi firma karşılaştırmasını kolaylaştıracaktır. Örneğin:  
  ![][image1]

- Aynı VKN olup farklı firma isimleri olan firmalar muhtemelen ünvan değişikliği yaşamıştır, burada son ünvanı dikkate alarak firma isimlerini tekilleştirebiliriz.  
- Aynı firma ismi farklı VKN olan firmaların öncelikle il bilgisine bakılır. İl bilgisi aynı ise büyük ihtimalle aynı firmadır, burada rastgele ilgili VKNlerden biri seçilir ve aynı isme sahip olanları aynı VKN’de hizalayabiliriz.  
- Firma isimlerinin birbirine benzer olması durumunda benzerlik oranlarına göre firmaların aynı olup olmadığı kontrol edilir. Örneğin Türk Hava Yolları Anonim Şirketi ve Türk Hava Yolları A.Ş. aynı şirkettir ama kısaltmadan dolayı farklı isimlendirilmiştir. Böyle durumlarda kural tabanlı benzerlik oranı yüksek olan firmaları aynı firma olarak alabiliriz.  
- Adresi, telefonu veya e-postası aynı olan firmalar aynı olarak kabul edilebilir. Bu alanlar yardımcı kontrol amaçlı kullanılabilir.  
- 

[image1]: <data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAATcAAAETCAIAAADcfdZFAAAR60lEQVR4Xu2dbY7kuK5EZxe52redt837PtTQsBkUTduZNp15DowBFQpRsksB1I8uzD+vH+A/Ed70A/zsiz+df7wAAM0gpQDdIaUA3SGlAN0hpQDdIaUA3SGlAN0hpQDdIaVV/vnv//rfx6sAn4eUViGlcBd3pnTe+68JwMk32ru8aHvtcSYc7lDZfdNQwW2093u2pV1Kw685DPNR8W97FV2+uVG+1zRsOkPC5Umf+i7qLC60bC4Zu6gtFB2bBsvK7Daaw8oBOvOMlA7clPt5mJkSyZJwKhQd4RvVCZcf6FPhQNt8ST67ya7lRXP4PZ/InSkNSb6mm8qHmyT+cCoU6xz+e5Rw33Ht7NRU7FN0hlO5qISz4cIxdFO2SHa3Q7fcLfwmfjelL7kQVvfSQqzz3pQO7NR8F1uEzqJSaeKYW6vuhlNxheugC0N9stKfzk+ndKALVVmJF5DsG15oLVydKO5xNl0SojanqGGIqodHGvrfxj+s9KdDSv+PSqtQvIBkXzulNzic3aU4MTSE5N8z7DPEivO1X386X5JSVydNQpLOibhrowt+410Vrk6UUJyFzq5wznzoRHfs3OxY6crhH8ctdEypfdyUG65slZ/WavmYcsPQHK5dcfhahLuraKdsoc45m4u2z2w1nYoudKJtOA0Tt8rWYYdECfs7Dv84bqFdSi+m8hOFOk/5nqT0MTzlSj2Fp3zPZ0X09eMpBXgEpBSgO6QUoDukFKA7pBSgO6QUoDukFKA7pBSgO6Q0oPivzACugZQGkFJoxZ0pnWFonoqT59y7vGh77XEmHO5Q2X3TUMFttPd7fgHtUhp+92GYj4p/2wPUudnTibbYRbg86VPfRZ3FhZbNJWMXtYWiY9NgWZndRnNYOcB38IyUDtyU+8mZGU8yG07pRnrOOuHyA30qHGibL8lnN9m1vGgOv+d3c2dKQ5Lv7qbyoWXvVCiGHP7rinCLce3s1FTsU3SGU7mohLPhwjF0U7ZIdrdDt9wt/E1+IqUvuSVW99JCDHlvSgd2ah7bFqGzqFSaOObWqrvhVFzhOujCUJ+s9N/hV1I6UI8qK/G9JFuEF1oLVyeKe5xNl4SozSlqGKLq4ZGG/rfxDyv9d/itlL7EFq4KxfeSbGGn9AaHs7sUJ4aGkPzThX2GWHG+9uu/w5ekNFnlSJrkYsgFv/GuClcnSijOQmdX5J8u7LM6dm52rHTl8I+jOR1Tah835YahTUmcSU81K4evRbiRinbKFuqcs7lo+8xW06noQifahtMwcatsHXZIlLC/4/CPozntUvo5Kj9mCHnKpyOlz+Yp96whT/l03xrR1++kFOC5kFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7tyZ0vmHDsW/eDjMyY32Li/aXnucCbPD3nPCU2iX0vB6DcN8VPzb7gk3qhMuT/rUd1FncaHFHknPCV/AM1I6SC50suq12KhOuPxAnwoH2toj6TnhC7gzpSHJ9XJT+bDI4T93Crcb8bBTU7FP0RlO5SJ8JaT0nSkdaJZcETqLSqUJfBm/ntLDJNuFEdLC1YniHmfTJfBlkNKDJNuFEdLC1XXFiaEBvokvSamrkyaOC37jXRWuTpRQnIXOKodfEzrQMaX2cVNuuLJVLu7g8PUNz6minbKFOudsLto+s9V0rjj8mtCBdimFT0BKHw0p/X6I6NMhpQDdIaUA3SGlAN0hpQDdIaUA3SGlAN0hpQDdIaUA3SGlAN0hpQDduTOl9t+LV/7JeIWTPfcuL9pee5wJs8Pec8KjaZfS8M4Nw3xUdE5b7CJcnvSp76LO4kKLPZKeE76VZ6R0kNzyd13fcPmBPhUOtH3Xa8KzuDOlIcmdc1P5UDn8pyFh5xEPOzUV+xSd4VQuwu9ASrdJOmuWXBE6i0qlCfwCP5TSwySdwwhp4epEcY+z6RL4BUjpNknnMEJauLquODE0wNfzJSlNVk0u+I13Vbg6UUJxFjqrHH5NaEvHlNrHTblhaFtx+PqGR1LRTtlCnXM2F22f2Wo6Vxx+TWhLu5TCSUjp90FKvwoi+pWQUoDukFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7tyZUvunHpW/9riLk+fcu7xoe+1xJswOe88Jl9EupeH9GIb5TNEacucBcShTtMUuwuVJn/ou6iwutNgj6TmhA89I6UBv5GpV2GSvxzbXc9YJlx/oU+FA23e9JnyOO1MaktwPN5XcqrDJuzyWw38pFnYeL2KnpmKfojOcykXoyfek1F3Bf30L0V7TOaUeO1Tem9KBexH7jjo766JSaQLdeHZK7X83b1643FHxvIWkc/giWrg6UdzjbLoEuvH4lM568+Yly1di6HkLSefwRbRwdV1xYmiAVjw1pf9ILDdvnhM3PaHBccFvvKvC1YkSirPQWeXwa8Jb6JhS+6xEd72c7pZPjxuqU5Wcw9d3c/ch2ilbqHPO5qLtM1tN54rDrwlvoV1KoSGk9F5IKWxARG+HlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdIeUAnSHlAJ0h5QCdOfOlNr/aXzl/xt/mJMbnVy+l0/sUum5aajgNrr4030rTVOqSsiwbTqTjSoky1U5z5meqw8Sio5Ng2VldhvNYeUAsKJjSqc4lZBNw2S1UZHV8uI5L+PkSXYtL5pXnw52cWdKVxRv/6ahwn/+H6/W0HOOu6g3UnVVrKhK6HeEU+GqMXRTtrB+t7UduuVuIbyLvim1xYrz1+J8Sl2xKY46dOpQ/c7ssG2d7oa6ux7MzrphuMtrrcMZ2qU0vEA5Rdt7Cc8Z3u/woodOHWqTysuqJ9nCiqoP0T5T/9v4h5UOZ2iaUr0WOUXbGwnPaY8xa3e2xKlDbVJ806SnDq1Ycb7263CGjikNh/+kiXU3O3E6Dv/GG57THWMWWodOHdqFOpuQ9NShE90BcrNjpSuHv/wP8uCUDmWlWyXh8F3RTZ3oanfU0GltrqEWiu7ixLyJW2XrsEOihP0dh7/8D9IupXupXIif4ikfhJTWeXZKn3IjL+MpH4SI7uLZKQX4BUgpQHdIKUB3SClAd0gpQHdIKUB3SClAd0gpQHdIKUB3SClAd0gpQHfuTOn844niX1FUONnz5PK9fGKXSs9NQwW30cWf7qdomlJVQobtjXclWa7Kec701He3uhMdmwbLyuw2msPKAWAXHVM6xamEhIZVzyKr5cUjXcbJk+xaXjSvPh2c586UrihGYtPgOPPXUnqkcRf1RqquihVVCf2OcCpcNYZuyhbW77a2Q7fcLYSP0jeltlix666cT6krNsVRh04dqt+ZHbat091Qd9eD2Vk3DHd5rXV4O+1SGt6qnKLtMOGRwvsdXvTQqUNtUnkv9SRbWFH1Idpn6n8b/7DS4e00TanelZyi7RjhkeyOs3bHSJw61CbFl0p66tCKFedrvw5vp2NKk+GKiu3wb7zhkaxoc6V16NShXaizCUlPHTrRHSA3O1a6cvjLw+DBKR23anW3lMN3JTySu9+2dqcKndbmGmqh6C5OzJu4VbYOOyRK2N9x+MvDoF1K91K5Jd/KU96dlJ7k2Sl9yjX9BE95dyJ6nmenFOAXIKUA3SGlAN0hpQDdIaUA3SGlAN0hpQDdIaUA3SGlAN0hpQDduTOl899qF//RtuPe5Xv5xC6VnpuGCm6jiz8dNE3pSnnjXUmWq3KeMz313a3uRMemwbIyu43msHIAOE/HlE7RKY7V8iKr5br7vZw8ya7lRfPq08GHuDOlKzQne6/Cmb/DCHefz7++SFfFiqqEfkc4Fa4aQzdlC+t3W9uhW+4WwvX0TaktRl2/K+dT6opNcdShU4fqd2aHbet0N9Td9WB21g3DXV5rHa6hXUrDqzZR5b2Eu4f3O7zooVOH2qTyXupJtrCi6kO0z9T/Nv5hpcM1NE2pXiBrcMobCXe3O87aHSNx6lCbFF8q6alDK1acr/06XEPHlCbDUFEO/8Yb7m5FmyutQ6cO7UKdTUh66tCJ7gC52bHSlcNfHhKekdJxq1Z3Szl8V1a7O2XW7lSh09pcQy0U3cWJeRO3ytZhh0QJ+zsOf3lIaJfShMot+Vae8u6k9BM8JqVPuaaf4CnvTkQ/xGNSCvCzkFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7tyZ0vlnFsW/t3Dcu3wvn9il0nPTUMFtdPGng6YpXSlvvCvJclXOc6anvrvVnejYNFhWZrfRHFYOAOfpmNIpOsWxWl5ktVx3v5eTJ9m1vGhefTr4EHemdIXmZO9VOPMnVOHu8/nXF+mqWFGV0O8Ip8JVY+imbGH9bms7dMvdQrievim1xajrd+V8Sl2xKY46dOpQ/c7ssG2d7oa6ux7MzrphuMtrrcM1tEtpeNUmqryXcPfwfocXPXTqUJtU3ks9yRZWVH2I9pn638Y/rHS4hqYp1QtkDU55I+HudsdZu2MkTh1qk+JLJT11aMWK87Vfh2vomNJkGCrK4d94w92taHOldejUoV2oswlJTx060R0gNztWunL4y0PCM1I6btXqbimH78pqd6fM2p0qdFqba6iFors4MW/iVtk67JAoYX/H4S8PCe1SCiGVhHSAlH4CUvoAiOiPQ0oBukNKAbpDSgG6Q0oBukNKAbpDSgG6Q0oBukNKAbrzQykt/hs3gG6QUoDu3JnSGZs++Tl5pJPL91LZ5eIjwSdomlKn2KHqblad1qaz6rTFLpLlqpyn0jM5EjyFjimdojqtrktGERrCoTqnaGfrrJZPcSqXsToSPIg7U7pC77S9YTrrdOd0BjvUJQln/uBDzzw21a1VV8WKqoR+eDR9U6qF/teis86jw2mr3OnzKXXFpmiP55w6VL8zw3Npl9LwUtprt7qCOus84XD+Vxu+i+SNEnEOQ6cOtcnn3ggupmlK7TNEO2uVycrpDOFQzW/Evkt4fnvmKc5h6NShNvncG8HFdEypDu3NC2+hitbppkadDEMO/8YbHmN1eK1Dpw7tQp0NOfxGcDHPTul8nD9xzoZulR2GHL7TrvM8gFNmbc+ps2pzDbVYcfiN4GLapRQug5Q+BVL6oxDRB0FKAbpDSgG6Q0oBukNKAbpDSgG6Q0oBukNKAbpDSgG6Q0oBuvNDKbX/OPZdfKJnyGUbQUNI6Sk+0TPkso2gIXemdN48ruBeTn66k8v38oldKj03DRXcRhd/ukHTlK4+zaid7mbVaW066+oQt9Yqrmfo1HpzeUW0xS6S5aqc50xP9+JOd6Jj02BZmd1Gc1g5wLvomNIpqtPqumQUoSEcrpyOZFZ7aq2Fqx3hlG40FPsWdVbLpziVezl5kl3Li+bVp/sod6Z0hd4V+0V01unO6Qx2qEtCklntqXV44HrPRAw58ydpetTxcfQTqa6KFVUJ/Y5wKlw1hm7KFtbvtrZDt9wtvIu+KdVC/2vRWefR4bRt/jBWBu2p9eow9nFTdpiIIedT6opNcdShU4fqd2aHbet0N9Td9WB21g3DXV5r/UrapTT/3O6/Fp1d/TzscP5XGyrq0Z7zcaJx/RGdMgmnQvG92HNqsRLnMHTqUJtUXk09yRZWVH2I9pn638Y/rPQraZpS9xHtp3TKZOV0hnCo5hVJEzu0DcMDJ9uFU6H4XsaZ7TNEa3CFHYZOHWqT4qslPXVoxYrztV+/ko4p1aH9iYY/XRWt002NOhkmJD3dMDySzirhVCiGHP6NN3yX8Mz2c806dOpQv0bx1ZKeOnSiO0Budqx05fCX3+TZKZ2P8yfO2dCtskPHaiPV1aCindW21jOHK2fI4bui+zrR1e5IoVMPr3670KG7ODFv4lbZOuyQKGF/x+Evv0m7lLal8nOCT/CUL09Kb+YpF+X7eMqX/1xEX6QUoD+kFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7pBSgO6QUoDukFKA7pBSgO6QUoDv/A2tPFi6j0etYAAAAAElFTkSuQmCC>